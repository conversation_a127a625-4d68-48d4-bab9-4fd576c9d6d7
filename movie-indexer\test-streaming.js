/**
 * Teste direto do serviço de streaming
 */

const streamingService = require('./src/services/streamingService');

async function testStreaming() {
    try {
        console.log('Testando serviço de streaming...');
        
        // Dados de teste do filme
        const movieData = {
            id: 6,
            title: 'Clube da Luta',
            year: 1999,
            imdb_id: 'tt0137523',
            tmdb_id: 550
        };
        
        console.log('Dados do filme:', movieData);
        
        // Testar busca de opções de streaming
        console.log('\nBuscando opções de streaming...');
        const result = await streamingService.getStreamingOptions(movieData);
        
        console.log('Resultado:', JSON.stringify(result, null, 2));
        
        if (result.success) {
            console.log('\n✅ Sucesso!');
            console.log('Qualidades encontradas:', Object.keys(result.options));
            
            Object.keys(result.options).forEach(quality => {
                console.log(`${quality}: ${result.options[quality].length} torrents`);
                if (result.options[quality].length > 0) {
                    console.log('  Primeiro torrent:', result.options[quality][0].title);
                }
            });
        } else {
            console.log('\n❌ Falha:', result.error);
        }
        
    } catch (error) {
        console.error('Erro no teste:', error.message);
        console.error('Stack:', error.stack);
    }
}

testStreaming();
