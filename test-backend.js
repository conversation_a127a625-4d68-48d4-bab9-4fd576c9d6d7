/**
 * Arquivo de teste para o backend do Movie Stash
 * Execute com: node test-backend.js
 */

const MovieStash = require('./backend/index');

async function testMovieStash() {
    console.log('🎬 === TESTE DO MOVIE STASH BACKEND ===\n');
    
    const movieStash = new MovieStash();
    
    try {
        // 1. Testar busca de filmes
        console.log('1️⃣ Testando busca de filmes...');
        const searchResults = await movieStash.searchAndCatalogMovie('Matrix');
        
        if (searchResults.length > 0) {
            console.log(`✅ Encontrados ${searchResults.length} filmes`);
            
            // 2. Adicionar primeiro filme ao catálogo
            console.log('\n2️⃣ Adicionando filme ao catálogo...');
            const movie = await movieStash.addMovieToLocalCatalog(searchResults[0].tmdbId);
            
            if (movie) {
                console.log(`✅ Filme adicionado: ${movie.title} (ID: ${movie.id})`);
                
                // 3. Listar filmes do catálogo
                console.log('\n3️⃣ Listando catálogo...');
                movieStash.listLocalMovies();
                
                // 4. Mostrar estatísticas
                console.log('\n4️⃣ Estatísticas...');
                movieStash.showStats();
                
                // 5. Testar adição de link de download
                console.log('\n5️⃣ Adicionando link de download...');
                const linkAdded = movieStash.catalogManager.addDownloadLink(movie.id, {
                    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                    type: 'http',
                    quality: '720p',
                    notes: 'Arquivo de teste'
                });
                
                if (linkAdded) {
                    console.log('✅ Link de download adicionado');
                }
                
                // 6. Testar download HTTP (com arquivo pequeno de exemplo)
                console.log('\n6️⃣ Testando download HTTP...');
                try {
                    const downloadResult = await movieStash.downloadMovieHttp(
                        movie.id, 
                        'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                        'teste_matrix'
                    );
                    
                    if (downloadResult) {
                        console.log('✅ Download HTTP concluído com sucesso!');
                    }
                } catch (downloadError) {
                    console.log('⚠️ Download HTTP falhou (normal se não tiver conexão):', downloadError.message);
                }
                
            } else {
                console.log('❌ Falha ao adicionar filme ao catálogo');
            }
        } else {
            console.log('❌ Nenhum filme encontrado');
        }
        
    } catch (error) {
        console.error('❌ Erro no teste:', error.message);
    } finally {
        // Limpa recursos
        movieStash.cleanup();
    }
}

// Executa teste
if (require.main === module) {
    testMovieStash().catch(console.error);
}

module.exports = testMovieStash;
