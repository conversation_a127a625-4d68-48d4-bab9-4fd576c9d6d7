const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// Importa os módulos do backend
const MovieStash = require('./backend/index');

// Instância global do MovieStash
let movieStash;
let mainWindow;

/**
 * Cria a janela principal da aplicação
 */
function createWindow() {
    // Cria a janela do navegador
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'frontend', 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'), // Adicione um ícone se tiver
        show: false, // Não mostra até estar pronto
        titleBarStyle: 'default'
    });

    // Carrega o arquivo HTML
    mainWindow.loadFile(path.join(__dirname, 'frontend', 'index.html'));

    // Mostra a janela quando estiver pronta
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Abre DevTools em modo de desenvolvimento
        if (process.argv.includes('--dev')) {
            mainWindow.webContents.openDevTools();
        }
    });

    // Evento quando a janela é fechada
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (movieStash) {
            movieStash.cleanup();
        }
    });

    // Previne navegação para URLs externas
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
            shell.openExternal(navigationUrl);
        }
    });
}

/**
 * Inicializa o MovieStash backend
 */
function initializeMovieStash() {
    try {
        movieStash = new MovieStash();
        console.log('✅ MovieStash backend inicializado');
    } catch (error) {
        console.error('❌ Erro ao inicializar MovieStash:', error.message);
    }
}

// Evento quando o Electron terminou de inicializar
app.whenReady().then(() => {
    initializeMovieStash();
    createWindow();
    
    // No macOS, recria a janela quando o ícone do dock é clicado
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

// Sai quando todas as janelas são fechadas (exceto no macOS)
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        if (movieStash) {
            movieStash.cleanup();
        }
        app.quit();
    }
});

// === IPC HANDLERS - Comunicação entre frontend e backend ===

/**
 * Busca filmes no TMDb
 */
ipcMain.handle('search-movies', async (event, query) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const results = await movieStash.searchAndCatalogMovie(query);
        return { success: true, data: results };
    } catch (error) {
        console.error('Erro ao buscar filmes:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Obtém detalhes de um filme específico
 */
ipcMain.handle('get-movie-details', async (event, tmdbId) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const details = await movieStash.tmdbApi.getMovieDetails(tmdbId);
        return { success: true, data: details };
    } catch (error) {
        console.error('Erro ao obter detalhes do filme:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Adiciona filme ao catálogo local
 */
ipcMain.handle('add-to-catalog', async (event, tmdbId) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const movie = await movieStash.addMovieToLocalCatalog(tmdbId);
        return { success: true, data: movie };
    } catch (error) {
        console.error('Erro ao adicionar ao catálogo:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Lista filmes do catálogo local
 */
ipcMain.handle('list-catalog-movies', async (event, filters = {}) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const movies = movieStash.catalogManager.listMovies(filters);
        return { success: true, data: movies };
    } catch (error) {
        console.error('Erro ao listar filmes do catálogo:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Obtém filme do catálogo por ID
 */
ipcMain.handle('get-catalog-movie', async (event, movieId) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const movie = movieStash.catalogManager.getMovieById(movieId);
        return { success: true, data: movie };
    } catch (error) {
        console.error('Erro ao obter filme do catálogo:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Adiciona link de download a um filme
 */
ipcMain.handle('add-download-link', async (event, movieId, downloadLink) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const success = movieStash.catalogManager.addDownloadLink(movieId, downloadLink);
        return { success, data: success };
    } catch (error) {
        console.error('Erro ao adicionar link de download:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Inicia download HTTP
 */
ipcMain.handle('download-http', async (event, movieId, downloadUrl, customFileName) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const success = await movieStash.downloadMovieHttp(movieId, downloadUrl, customFileName);
        return { success, data: success };
    } catch (error) {
        console.error('Erro no download HTTP:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Inicia download via torrent
 */
ipcMain.handle('download-torrent', async (event, movieId, magnetOrTorrentUrl) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const success = await movieStash.downloadMovieTorrent(movieId, magnetOrTorrentUrl);
        return { success, data: success };
    } catch (error) {
        console.error('Erro no download via torrent:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Inicia download via yt-dlp
 */
ipcMain.handle('download-ytdlp', async (event, movieId, videoUrl, options) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const success = await movieStash.downloadMovieYtDlp(movieId, videoUrl, options);
        return { success, data: success };
    } catch (error) {
        console.error('Erro no download via yt-dlp:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Remove filme do catálogo
 */
ipcMain.handle('remove-from-catalog', async (event, movieId) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const success = movieStash.catalogManager.removeMovie(movieId);
        return { success, data: success };
    } catch (error) {
        console.error('Erro ao remover filme do catálogo:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Obtém estatísticas do catálogo
 */
ipcMain.handle('get-catalog-stats', async (event) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const stats = movieStash.catalogManager.getStats();
        return { success: true, data: stats };
    } catch (error) {
        console.error('Erro ao obter estatísticas:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Abre pasta de downloads
 */
ipcMain.handle('open-downloads-folder', async (event) => {
    try {
        const downloadsPath = path.join(__dirname, 'downloads');
        
        // Cria a pasta se não existir
        if (!fs.existsSync(downloadsPath)) {
            fs.mkdirSync(downloadsPath, { recursive: true });
        }
        
        shell.openPath(downloadsPath);
        return { success: true };
    } catch (error) {
        console.error('Erro ao abrir pasta de downloads:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Seleciona pasta de destino para downloads
 */
ipcMain.handle('select-download-folder', async (event) => {
    try {
        const result = await dialog.showOpenDialog(mainWindow, {
            properties: ['openDirectory'],
            title: 'Selecionar pasta de downloads'
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
            return { success: true, data: result.filePaths[0] };
        }
        
        return { success: false, error: 'Seleção cancelada' };
    } catch (error) {
        console.error('Erro ao selecionar pasta:', error.message);
        return { success: false, error: error.message };
    }
});

/**
 * Obtém filmes populares do TMDb
 */
ipcMain.handle('get-popular-movies', async (event, page = 1) => {
    try {
        if (!movieStash) {
            throw new Error('MovieStash não inicializado');
        }
        
        const results = await movieStash.tmdbApi.getPopularMovies(page);
        return { success: true, data: results };
    } catch (error) {
        console.error('Erro ao obter filmes populares:', error.message);
        return { success: false, error: error.message };
    }
});

// Tratamento de erros não capturados
process.on('uncaughtException', (error) => {
    console.error('Erro não capturado:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Promise rejeitada não tratada:', reason);
});
