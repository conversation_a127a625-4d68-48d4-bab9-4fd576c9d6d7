const express = require('express');
const { param, body, query, validationResult } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const streamingService = require('../services/streamingService');
const Movie = require('../models/Movie');
const logger = require('../utils/logger');

/**
 * Middleware de validação
 */
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Dados inválidos',
            details: errors.array()
        });
    }
    next();
};

const router = express.Router();

/**
 * GET /api/streaming/options/:movieId
 * Busca opções de streaming para um filme
 */
router.get('/options/:movieId', [
    param('movieId').isInt({ min: 1 }).withMessage('ID do filme deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movie = await Movie.findById(req.params.movieId);
    
    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    const streamingOptions = await streamingService.getStreamingOptions(movie);
    
    logger.activity('search', 'streaming_options', { 
        movieId: movie.id, 
        title: movie.title,
        optionsFound: Object.values(streamingOptions.options).reduce((sum, opts) => sum + opts.length, 0)
    }, req.ip);

    res.json(streamingOptions);
}));

/**
 * POST /api/streaming/start
 * Inicia streaming de um torrent
 */
router.post('/start', [
    body('magnet').notEmpty().withMessage('Magnet link é obrigatório'),
    body('quality').optional().isString().withMessage('Qualidade deve ser uma string'),
    body('movieId').optional().isInt({ min: 1 }).withMessage('ID do filme deve ser um número positivo'),
    body('subtitleLanguage').optional().isString().withMessage('Idioma da legenda deve ser uma string')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const { magnet, quality, movieId, subtitleLanguage } = req.body;
    
    const streamId = streamingService.generateStreamId();
    
    const result = await streamingService.startStream(streamId, magnet, {
        quality,
        movieId,
        subtitleLanguage
    });

    if (result.success) {
        logger.activity('start', 'stream', { 
            streamId: result.streamId, 
            quality: quality || 'unknown',
            movieId: movieId || null
        }, req.ip);
    }

    res.json(result);
}));

/**
 * POST /api/streaming/stop/:streamId
 * Para um stream ativo
 */
router.post('/stop/:streamId', [
    param('streamId').notEmpty().withMessage('ID do stream é obrigatório')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const result = await streamingService.stopStream(req.params.streamId);
    
    if (result.success) {
        logger.activity('stop', 'stream', { streamId: req.params.streamId }, req.ip);
    }

    res.json(result);
}));

/**
 * GET /api/streaming/status/:streamId
 * Obtém status de um stream
 */
router.get('/status/:streamId', [
    param('streamId').notEmpty().withMessage('ID do stream é obrigatório')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const status = streamingService.getStreamStatus(req.params.streamId);
    
    if (status) {
        res.json({
            success: true,
            status: status
        });
    } else {
        res.status(404).json({
            success: false,
            error: 'Stream não encontrado'
        });
    }
}));

/**
 * GET /api/streaming/active
 * Lista streams ativos
 */
router.get('/active', asyncHandler(async (req, res) => {
    const activeStreams = streamingService.getActiveStreams();
    
    res.json({
        success: true,
        streams: activeStreams,
        count: activeStreams.length
    });
}));

/**
 * GET /api/streaming/history
 * Obtém histórico de streaming
 */
router.get('/history', [
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit deve ser entre 1 e 100')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const limit = parseInt(req.query.limit) || 20;
    const history = streamingService.getStreamingHistory().slice(0, limit);
    
    res.json({
        success: true,
        history: history,
        count: history.length
    });
}));

/**
 * GET /api/streaming/subtitles/:movieId
 * Busca legendas para um filme
 */
router.get('/subtitles/:movieId', [
    param('movieId').isInt({ min: 1 }).withMessage('ID do filme deve ser um número positivo'),
    query('languages').optional().isString().withMessage('Idiomas devem ser uma string')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movie = await Movie.findById(req.params.movieId);
    
    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    const languages = req.query.languages ? req.query.languages.split(',') : ['pt', 'en'];
    const subtitleService = require('../services/subtitleService');
    
    const subtitles = await subtitleService.searchSubtitles(movie, languages);
    
    logger.activity('search', 'subtitles', { 
        movieId: movie.id, 
        title: movie.title,
        languages: languages,
        subtitlesFound: Object.values(subtitles).reduce((sum, subs) => sum + subs.length, 0)
    }, req.ip);

    res.json({
        success: true,
        movie: {
            id: movie.id,
            title: movie.title
        },
        subtitles: subtitles,
        languages: languages
    });
}));

/**
 * POST /api/streaming/subtitles/download
 * Baixa uma legenda específica
 */
router.post('/subtitles/download', [
    body('subtitleId').notEmpty().withMessage('ID da legenda é obrigatório'),
    body('provider').notEmpty().withMessage('Provedor é obrigatório'),
    body('language').notEmpty().withMessage('Idioma é obrigatório'),
    body('downloadUrl').notEmpty().withMessage('URL de download é obrigatória')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const { subtitleId, provider, language, downloadUrl, filename } = req.body;
    
    const subtitleData = {
        id: subtitleId,
        provider: provider,
        language: language,
        downloadUrl: downloadUrl,
        filename: filename || 'subtitle.srt'
    };

    const result = await streamingService.downloadSubtitle(subtitleData);
    
    if (result.success) {
        logger.activity('download', 'subtitle', { 
            subtitleId: subtitleId,
            provider: provider,
            language: language
        }, req.ip);

        // Retornar legenda como WebVTT para uso direto no player
        res.setHeader('Content-Type', 'text/vtt; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${subtitleData.filename.replace('.srt', '.vtt')}"`);
        
        res.send(result.subtitle.vttContent || result.subtitle.content);
    } else {
        res.status(500).json(result);
    }
}));

/**
 * GET /api/streaming/addons
 * Lista addons disponíveis
 */
router.get('/addons', asyncHandler(async (req, res) => {
    const addonManager = require('../services/addonManager');
    const availableAddons = addonManager.getAvailableAddons();
    
    res.json({
        success: true,
        addons: availableAddons,
        count: availableAddons.length
    });
}));

/**
 * GET /api/streaming/test/:addon
 * Testa conectividade com um addon
 */
router.get('/test/:addon', [
    param('addon').notEmpty().withMessage('Nome do addon é obrigatório')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const addonName = req.params.addon;
    
    if (addonName === 'torrentio') {
        const TorrentioAddon = require('../addons/torrentio');
        const torrentio = new TorrentioAddon();
        const result = await torrentio.testConnection();
        
        res.json({
            success: true,
            addon: addonName,
            test: result
        });
    } else {
        res.status(404).json({
            success: false,
            error: 'Addon não encontrado'
        });
    }
}));

/**
 * POST /api/streaming/session/start
 * Inicia sessão de streaming
 */
router.post('/session/start', [
    body('movie_id').isInt({ min: 1 }).withMessage('ID do filme é obrigatório'),
    body('quality').isLength({ min: 1 }).withMessage('Qualidade é obrigatória'),
    body('provider').isLength({ min: 1 }).withMessage('Provedor é obrigatório'),
    body('magnet').isLength({ min: 1 }).withMessage('Magnet link é obrigatório')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const { movie_id, quality, provider, magnet } = req.body;

    // Buscar dados do filme
    const Movie = require('../models/Movie');
    const movie = await Movie.findById(movie_id);

    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    const session = await streamingService.startStreamingSession(movie, {
        quality,
        provider,
        magnet
    });

    res.json({
        success: true,
        data: session,
        message: 'Sessão de streaming iniciada'
    });
}));

/**
 * PUT /api/streaming/session/:id/progress
 * Atualiza progresso da sessão
 */
router.put('/session/:id/progress', [
    param('id').isInt({ min: 1 }).withMessage('ID da sessão inválido'),
    body('progress').isFloat({ min: 0, max: 100 }).withMessage('Progresso deve ser entre 0 e 100'),
    body('duration').optional().isInt({ min: 0 }).withMessage('Duração deve ser positiva')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const { progress, duration } = req.body;

    const session = await streamingService.updateStreamingProgress(
        req.params.id,
        progress,
        duration
    );

    if (!session) {
        return res.status(404).json({
            success: false,
            error: 'Sessão não encontrada'
        });
    }

    res.json({
        success: true,
        data: session,
        message: 'Progresso atualizado'
    });
}));

/**
 * POST /api/streaming/session/:id/end
 * Finaliza sessão de streaming
 */
router.post('/session/:id/end', [
    param('id').isInt({ min: 1 }).withMessage('ID da sessão inválido'),
    body('rating').optional().isInt({ min: 1, max: 10 }).withMessage('Avaliação deve ser entre 1 e 10'),
    body('notes').optional().isLength({ max: 500 }).withMessage('Notas devem ter até 500 caracteres')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const { rating, notes } = req.body;

    const session = await streamingService.endStreamingSession(
        req.params.id,
        rating,
        notes
    );

    if (!session) {
        return res.status(404).json({
            success: false,
            error: 'Sessão não encontrada'
        });
    }

    res.json({
        success: true,
        data: session,
        message: 'Sessão finalizada'
    });
}));

/**
 * GET /api/streaming/history/db
 * Obtém histórico de streaming do banco
 */
router.get('/history/db', [
    query('movie_id').optional().isInt({ min: 1 }).withMessage('ID do filme inválido'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite deve ser entre 1 e 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset deve ser positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const { movie_id, limit = 20, offset = 0 } = req.query;

    const history = await streamingService.getStreamingHistoryFromDB(
        movie_id ? parseInt(movie_id) : null,
        parseInt(limit),
        parseInt(offset)
    );

    res.json({
        success: true,
        data: history,
        pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            count: history.length
        }
    });
}));

/**
 * GET /api/streaming/stats
 * Obtém estatísticas de streaming
 */
router.get('/stats', asyncHandler(async (req, res) => {
    const stats = await streamingService.getStreamingStats();

    res.json({
        success: true,
        data: stats
    });
}));

module.exports = router;
