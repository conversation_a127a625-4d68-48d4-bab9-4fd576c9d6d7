/**
 * Middleware de Tratamento de Erros
 */

const logger = require('../utils/logger');

/**
 * Middleware de tratamento de erros
 */
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;

    // Log do erro
    logger.error('Erro na aplicação:', {
        error: error.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    // Erro de validação do Express Validator
    if (err.name === 'ValidationError') {
        const message = 'Dados de entrada inválidos';
        error = { message, statusCode: 400 };
    }

    // Erro de cast do banco de dados
    if (err.name === 'CastError') {
        const message = 'Recurso não encontrado';
        error = { message, statusCode: 404 };
    }

    // Erro de duplicação (SQLite)
    if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        const message = 'Recurso já existe';
        error = { message, statusCode: 409 };
    }

    // Erro de chave estrangeira (SQLite)
    if (err.code === 'SQLITE_CONSTRAINT_FOREIGNKEY') {
        const message = 'Referência inválida';
        error = { message, statusCode: 400 };
    }

    // Erro de sintaxe JSON
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        const message = 'JSON inválido';
        error = { message, statusCode: 400 };
    }

    // Erro de timeout
    if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        const message = 'Timeout na requisição';
        error = { message, statusCode: 408 };
    }

    // Erro de rate limiting
    if (err.status === 429) {
        const message = 'Muitas requisições. Tente novamente mais tarde.';
        error = { message, statusCode: 429 };
    }

    // Resposta de erro
    res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Erro interno do servidor',
        ...(process.env.NODE_ENV === 'development' && {
            stack: err.stack,
            details: err
        })
    });
};

/**
 * Middleware para capturar rotas não encontradas
 */
const notFound = (req, res, next) => {
    const message = `Rota não encontrada: ${req.originalUrl}`;
    logger.warn(message, {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip
    });
    
    res.status(404).json({
        success: false,
        error: message
    });
};

/**
 * Wrapper para funções async
 */
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
    errorHandler,
    notFound,
    asyncHandler
};
