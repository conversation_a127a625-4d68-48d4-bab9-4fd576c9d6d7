/**
 * Movie Indexer - Servidor Principal
 * Sistema de indexação pessoal de filmes com API REST
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const path = require('path');

// Importar configurações e middleware
const config = require('./src/config/database');
const errorHandlerModule = require('./src/middleware/errorHandler');
const logger = require('./src/utils/logger');

// Importar rotas
// const movieRoutes = require('./src/routes/movies');
// const tmdbRoutes = require('./src/routes/tmdb');
// const linkRoutes = require('./src/routes/links');
// const healthRoutes = require('./src/routes/health');

const app = express();
const PORT = process.env.PORT || 3000;

// ===== MIDDLEWARE DE SEGURANÇA =====

// Helmet para headers de segurança
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:", "http:"],
            connectSrc: ["'self'", "https://api.themoviedb.org"],
            fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
        },
    },
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutos
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // máximo 100 requests por IP
    message: {
        error: 'Muitas requisições deste IP, tente novamente em alguns minutos.',
        retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000) / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Speed limiting para requisições pesadas
const speedLimiter = slowDown({
    windowMs: 15 * 60 * 1000, // 15 minutos
    delayAfter: 50, // permitir 50 requests por 15 minutos sem delay
    delayMs: () => 500, // adicionar 500ms de delay por request após o limite
    maxDelayMs: 20000, // máximo 20 segundos de delay
    validate: { delayMs: false } // desabilitar warning
});

app.use('/api/', limiter);
app.use('/api/', speedLimiter);

// ===== MIDDLEWARE GERAL =====

// Compressão
app.use(compression());

// CORS
app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com'] 
        : ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Parsing de JSON e URL
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
if (process.env.NODE_ENV !== 'test') {
    app.use(morgan('combined', {
        stream: {
            write: (message) => logger.info(message.trim())
        }
    }));
}

// Servir arquivos estáticos
app.use(express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// ===== ROTAS =====

// Health check básico
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Rotas principais da API (temporariamente comentadas)
// app.use('/api/movies', movieRoutes);
// app.use('/api/tmdb', tmdbRoutes);
// app.use('/api/links', linkRoutes);

// Rota para servir o frontend
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Rota catch-all para SPA
app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
        return res.status(404).json({
            success: false,
            error: 'Endpoint não encontrado',
            path: req.path
        });
    }
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// ===== MIDDLEWARE DE ERRO =====
app.use(errorHandlerModule.errorHandler);

// ===== INICIALIZAÇÃO =====

async function startServer() {
    try {
        // Inicializar banco de dados
        await config.initializeDatabase();
        logger.info('✅ Banco de dados inicializado');

        // Iniciar servidor
        const server = app.listen(PORT, () => {
            logger.info(`🚀 Movie Indexer rodando na porta ${PORT}`);
            logger.info(`📱 Frontend: http://localhost:${PORT}`);
            logger.info(`🔗 API: http://localhost:${PORT}/api`);
            logger.info(`📊 Health: http://localhost:${PORT}/api/health`);
            
            if (process.env.NODE_ENV === 'development') {
                logger.info('🔧 Modo de desenvolvimento ativo');
            }
        });

        // Graceful shutdown
        process.on('SIGTERM', () => {
            logger.info('🛑 SIGTERM recebido, encerrando servidor...');
            server.close(() => {
                logger.info('✅ Servidor encerrado graciosamente');
                process.exit(0);
            });
        });

        process.on('SIGINT', () => {
            logger.info('🛑 SIGINT recebido, encerrando servidor...');
            server.close(() => {
                logger.info('✅ Servidor encerrado graciosamente');
                process.exit(0);
            });
        });

    } catch (error) {
        logger.error('❌ Erro ao iniciar servidor:', error);
        process.exit(1);
    }
}

// Iniciar servidor apenas se não estiver sendo importado
if (require.main === module) {
    startServer();
}

module.exports = app;
