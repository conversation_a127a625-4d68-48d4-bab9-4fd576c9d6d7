/**
 * Teste direto da API do Torrentio
 */

const axios = require('axios');

async function testTorrentioDirect() {
    try {
        console.log('Testando API do Torrentio diretamente...');
        
        const url = 'https://torrentio.strem.fun/stream/movie/tt0137523.json';
        console.log('URL:', url);
        
        const response = await axios.get(url, {
            timeout: 10000,
            headers: {
                'User-Agent': 'MovieStash/1.0'
            }
        });
        
        console.log('Status:', response.status);
        console.log('Dados recebidos:', response.data ? 'Sim' : 'Não');
        
        if (response.data && response.data.streams) {
            console.log('Número de streams:', response.data.streams.length);
            
            // Mostrar os primeiros 3 streams
            console.log('\nPrimeiros 3 streams:');
            response.data.streams.slice(0, 3).forEach((stream, index) => {
                console.log(`\nStream ${index + 1}:`);
                console.log('  Estrutura completa:', JSON.stringify(stream, null, 2));
            });
        } else {
            console.log('Nenhum stream encontrado');
        }
        
    } catch (error) {
        console.error('Erro:', error.message);
        if (error.response) {
            console.error('Status HTTP:', error.response.status);
            console.error('Dados da resposta:', error.response.data);
        }
    }
}

testTorrentioDirect();
