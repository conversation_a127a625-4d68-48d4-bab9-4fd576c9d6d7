const logger = require('../utils/logger');

/**
 * Gerenciador de Addons para Streaming
 * Permite registrar e usar diferentes provedores de torrent
 */
class AddonManager {
    constructor() {
        this.addons = new Map();
        this.defaultAddons = ['torrentio', 'rarbg', 'yts'];
    }

    /**
     * Registra um novo addon
     */
    registerAddon(name, addonInstance) {
        if (!addonInstance.search || typeof addonInstance.search !== 'function') {
            throw new Error(`Addon ${name} deve implementar o método search`);
        }

        this.addons.set(name, addonInstance);
        logger.info(`Addon registrado: ${name}`);
    }

    /**
     * Remove um addon
     */
    unregisterAddon(name) {
        if (this.addons.has(name)) {
            this.addons.delete(name);
            logger.info(`Addon removido: ${name}`);
        }
    }

    /**
     * Lista todos os addons disponíveis
     */
    getAvailableAddons() {
        return Array.from(this.addons.keys());
    }

    /**
     * Busca torrents em todos os addons ou em addons específicos
     */
    async searchTorrents(query, options = {}) {
        const {
            addons = this.getAvailableAddons(),
            imdbId = null,
            tmdbId = null,
            season = null,
            episode = null,
            year = null
        } = options;

        const results = [];
        const searchPromises = [];

        for (const addonName of addons) {
            if (this.addons.has(addonName)) {
                const addon = this.addons.get(addonName);
                
                const searchPromise = this.searchWithTimeout(
                    addon,
                    query,
                    { imdbId, tmdbId, season, episode, year },
                    addonName
                );
                
                searchPromises.push(searchPromise);
            }
        }

        const addonResults = await Promise.allSettled(searchPromises);

        addonResults.forEach((result, index) => {
            const addonName = addons[index];
            
            if (result.status === 'fulfilled' && result.value) {
                results.push({
                    addon: addonName,
                    torrents: result.value
                });
            } else if (result.status === 'rejected') {
                logger.warn(`Erro no addon ${addonName}:`, result.reason.message);
            }
        });

        return this.organizeTorrents(results);
    }

    /**
     * Busca com timeout para evitar travamentos
     */
    async searchWithTimeout(addon, query, options, addonName, timeout = 10000) {
        return Promise.race([
            addon.search(query, options),
            new Promise((_, reject) => 
                setTimeout(() => reject(new Error(`Timeout no addon ${addonName}`)), timeout)
            )
        ]);
    }

    /**
     * Organiza torrents por qualidade e remove duplicatas
     */
    organizeTorrents(results) {
        const allTorrents = [];
        
        results.forEach(result => {
            result.torrents.forEach(torrent => {
                allTorrents.push({
                    ...torrent,
                    addon: result.addon
                });
            });
        });

        // Remover duplicatas baseado no hash ou magnet
        const uniqueTorrents = this.removeDuplicates(allTorrents);
        
        // Organizar por qualidade
        return this.sortByQuality(uniqueTorrents);
    }

    /**
     * Remove torrents duplicados
     */
    removeDuplicates(torrents) {
        const seen = new Set();
        return torrents.filter(torrent => {
            const identifier = torrent.hash || torrent.magnet || torrent.title;
            if (seen.has(identifier)) {
                return false;
            }
            seen.add(identifier);
            return true;
        });
    }

    /**
     * Ordena torrents por qualidade (4K > 1080p > 720p > 480p)
     */
    sortByQuality(torrents) {
        const qualityOrder = {
            '4k': 4,
            '2160p': 4,
            '1080p': 3,
            '720p': 2,
            '480p': 1
        };

        return torrents.sort((a, b) => {
            const qualityA = this.extractQuality(a.title || a.name || '');
            const qualityB = this.extractQuality(b.title || b.name || '');
            
            const orderA = qualityOrder[qualityA] || 0;
            const orderB = qualityOrder[qualityB] || 0;
            
            if (orderA !== orderB) {
                return orderB - orderA; // Maior qualidade primeiro
            }
            
            // Se mesma qualidade, ordenar por seeders
            return (b.seeders || 0) - (a.seeders || 0);
        });
    }

    /**
     * Extrai qualidade do título
     */
    extractQuality(title) {
        const titleLower = title.toLowerCase();
        
        if (titleLower.includes('4k') || titleLower.includes('2160p')) {
            return '4k';
        } else if (titleLower.includes('1080p')) {
            return '1080p';
        } else if (titleLower.includes('720p')) {
            return '720p';
        } else if (titleLower.includes('480p')) {
            return '480p';
        }
        
        return 'unknown';
    }

    /**
     * Busca torrents para um filme específico
     */
    async searchMovie(movieData) {
        const queries = this.generateMovieQueries(movieData);
        const allResults = [];

        for (const query of queries) {
            try {
                const results = await this.searchTorrents(query, {
                    imdbId: movieData.imdb_id,
                    tmdbId: movieData.tmdb_id,
                    year: movieData.year
                });
                
                allResults.push(...results);
            } catch (error) {
                logger.warn(`Erro na busca com query "${query}":`, error.message);
            }
        }

        return this.organizeTorrents([{ addon: 'combined', torrents: allResults }]);
    }

    /**
     * Gera diferentes queries para busca
     */
    generateMovieQueries(movieData) {
        const queries = [];
        const title = movieData.title || movieData.original_title || '';
        const year = movieData.year || '';

        if (title) {
            queries.push(title);
            if (year) {
                queries.push(`${title} ${year}`);
            }
        }

        if (movieData.original_title && movieData.original_title !== title) {
            queries.push(movieData.original_title);
            if (year) {
                queries.push(`${movieData.original_title} ${year}`);
            }
        }

        return queries;
    }
}

module.exports = new AddonManager();
