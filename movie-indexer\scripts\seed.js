/**
 * Script de Seed - Popula banco com dados de exemplo
 */

require('dotenv').config();
const database = require('../src/config/database');
const Movie = require('../src/models/Movie');
const MovieLink = require('../src/models/MovieLink');
const tmdbService = require('../src/services/tmdbService');
const logger = require('../src/utils/logger');

async function seedDatabase() {
    try {
        console.log('🌱 Iniciando seed do banco de dados...');
        
        // Inicializar banco
        await database.initializeDatabase();
        
        // Filmes populares para adicionar
        const popularMovies = [
            'The Matrix',
            'Inception',
            'Interstellar',
            'The Dark Knight',
            'Pulp Fiction',
            'Fight Club',
            'Forrest Gump',
            'The Godfather',
            'Shawshank Redemption',
            'Avatar'
        ];
        
        console.log('🎬 Buscando e adicionando filmes populares...');
        
        for (const movieTitle of popularMovies) {
            try {
                console.log(`Buscando: ${movieTitle}`);
                
                // Buscar filme no TMDb
                const searchResult = await tmdbService.searchMovies(movieTitle);
                
                if (searchResult.results && searchResult.results.length > 0) {
                    const tmdbMovie = searchResult.results[0];
                    
                    // Verificar se já existe
                    const existingMovie = await Movie.findByTmdbId(tmdbMovie.tmdb_id);
                    if (existingMovie) {
                        console.log(`  ⏭️ ${movieTitle} já existe no catálogo`);
                        continue;
                    }
                    
                    // Obter detalhes completos
                    const movieDetails = await tmdbService.getMovieDetails(tmdbMovie.tmdb_id);
                    
                    // Criar filme no catálogo
                    const movie = new Movie(movieDetails);
                    await movie.save();
                    
                    console.log(`  ✅ ${movieTitle} adicionado (ID: ${movie.id})`);
                    
                    // Adicionar alguns links de exemplo
                    await addSampleLinks(movie.id, movieTitle);
                    
                    // Pequena pausa para não sobrecarregar a API
                    await sleep(500);
                } else {
                    console.log(`  ❌ ${movieTitle} não encontrado no TMDb`);
                }
            } catch (error) {
                console.error(`  ❌ Erro ao processar ${movieTitle}:`, error.message);
            }
        }
        
        // Mostrar estatísticas
        const stats = await database.getStats();
        console.log('\n📊 Estatísticas do banco:');
        console.log(`  🎬 Filmes: ${stats.movies}`);
        console.log(`  🔗 Links: ${stats.links}`);
        console.log(`  📁 Coleções: ${stats.collections}`);
        
        console.log('\n✅ Seed concluído com sucesso!');
        
    } catch (error) {
        console.error('❌ Erro no seed:', error);
        process.exit(1);
    } finally {
        await database.close();
        process.exit(0);
    }
}

/**
 * Adiciona links de exemplo para um filme
 */
async function addSampleLinks(movieId, movieTitle) {
    const sampleLinks = [
        {
            url: `https://example.com/movies/${movieTitle.toLowerCase().replace(/\s+/g, '-')}-1080p.mp4`,
            provider: 'http',
            resolution: '1080p',
            quality: 'bluray',
            language: 'pt-BR'
        },
        {
            url: `https://drive.google.com/file/d/**********/view?usp=sharing`,
            provider: 'gdrive',
            resolution: '720p',
            quality: 'webrip',
            language: 'pt-BR'
        },
        {
            url: `https://mega.nz/file/abcdefgh#**********`,
            provider: 'mega',
            resolution: '1080p',
            quality: 'webdl',
            language: 'en'
        }
    ];
    
    for (const linkData of sampleLinks) {
        try {
            const link = new MovieLink({
                movie_id: movieId,
                ...linkData
            });
            await link.save();
            console.log(`    🔗 Link ${linkData.provider} ${linkData.resolution} adicionado`);
        } catch (error) {
            console.error(`    ❌ Erro ao adicionar link:`, error.message);
        }
    }
}

/**
 * Pausa execução
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Executar seed se chamado diretamente
if (require.main === module) {
    seedDatabase();
}

module.exports = seedDatabase;
