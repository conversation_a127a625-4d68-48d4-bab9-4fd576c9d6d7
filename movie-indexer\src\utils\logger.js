/**
 * Sistema de Logging
 */

const fs = require('fs');
const path = require('path');

class Logger {
    constructor() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.logFile = process.env.LOG_FILE || './logs/app.log';
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        
        this.ensureLogDirectory();
    }

    /**
     * Garante que o diretório de logs existe
     */
    ensureLogDirectory() {
        const logDir = path.dirname(this.logFile);
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
    }

    /**
     * Formata a mensagem de log
     */
    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}`;
    }

    /**
     * Escreve log no arquivo
     */
    writeToFile(formattedMessage) {
        if (process.env.NODE_ENV !== 'test') {
            fs.appendFileSync(this.logFile, formattedMessage + '\n');
        }
    }

    /**
     * Verifica se deve logar baseado no nível
     */
    shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }

    /**
     * Log de erro
     */
    error(message, meta = {}) {
        if (this.shouldLog('error')) {
            const formatted = this.formatMessage('error', message, meta);
            console.error('\x1b[31m%s\x1b[0m', formatted); // Vermelho
            this.writeToFile(formatted);
        }
    }

    /**
     * Log de aviso
     */
    warn(message, meta = {}) {
        if (this.shouldLog('warn')) {
            const formatted = this.formatMessage('warn', message, meta);
            console.warn('\x1b[33m%s\x1b[0m', formatted); // Amarelo
            this.writeToFile(formatted);
        }
    }

    /**
     * Log de informação
     */
    info(message, meta = {}) {
        if (this.shouldLog('info')) {
            const formatted = this.formatMessage('info', message, meta);
            console.log('\x1b[36m%s\x1b[0m', formatted); // Ciano
            this.writeToFile(formatted);
        }
    }

    /**
     * Log de debug
     */
    debug(message, meta = {}) {
        if (this.shouldLog('debug')) {
            const formatted = this.formatMessage('debug', message, meta);
            console.log('\x1b[90m%s\x1b[0m', formatted); // Cinza
            this.writeToFile(formatted);
        }
    }

    /**
     * Log de requisição HTTP
     */
    http(method, url, statusCode, responseTime, ip) {
        const message = `${method} ${url} ${statusCode} ${responseTime}ms - ${ip}`;
        this.info(message);
    }

    /**
     * Log de atividade do usuário
     */
    activity(action, resource, details = {}, ip = 'unknown') {
        const message = `User activity: ${action} ${resource}`;
        this.info(message, { details, ip });
    }

    /**
     * Limpa logs antigos
     */
    cleanOldLogs(daysToKeep = 30) {
        try {
            const logDir = path.dirname(this.logFile);
            const files = fs.readdirSync(logDir);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            files.forEach(file => {
                const filePath = path.join(logDir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime < cutoffDate) {
                    fs.unlinkSync(filePath);
                    this.info(`Log antigo removido: ${file}`);
                }
            });
        } catch (error) {
            this.error('Erro ao limpar logs antigos:', { error: error.message });
        }
    }

    /**
     * Obtém estatísticas dos logs
     */
    getLogStats() {
        try {
            const stats = fs.statSync(this.logFile);
            return {
                size: stats.size,
                sizeFormatted: this.formatBytes(stats.size),
                lastModified: stats.mtime,
                exists: true
            };
        } catch (error) {
            return {
                size: 0,
                sizeFormatted: '0 B',
                lastModified: null,
                exists: false
            };
        }
    }

    /**
     * Formata bytes para formato legível
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Instância singleton
const logger = new Logger();

module.exports = logger;
