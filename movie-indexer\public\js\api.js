/**
 * API Client - Comunicação com o backend
 */

class ApiClient {
    constructor() {
        this.baseUrl = '/api';
        this.timeout = 30000;
    }

    /**
     * Faz requisição HTTP
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Adicionar timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        config.signal = controller.signal;

        try {
            const response = await fetch(url, config);
            clearTimeout(timeoutId);

            if (!response.ok) {
                const error = await response.json().catch(() => ({ error: 'Erro desconhecido' }));
                throw new Error(error.error || `HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('Timeout na requisição');
            }
            throw error;
        }
    }

    /**
     * GET request
     */
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== '') {
                url.searchParams.append(key, params[key]);
            }
        });

        return this.request(url.pathname + url.search);
    }

    /**
     * POST request
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT request
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE request
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    // ===== MOVIES API =====

    /**
     * Lista filmes do catálogo
     */
    async getMovies(params = {}) {
        return this.get('/movies', params);
    }

    /**
     * Obtém detalhes de um filme
     */
    async getMovie(id) {
        return this.get(`/movies/${id}`);
    }

    /**
     * Cria novo filme
     */
    async createMovie(data) {
        return this.post('/movies', data);
    }

    /**
     * Atualiza filme
     */
    async updateMovie(id, data) {
        return this.put(`/movies/${id}`, data);
    }

    /**
     * Remove filme
     */
    async deleteMovie(id) {
        return this.delete(`/movies/${id}`);
    }

    /**
     * Obtém filmes recentes
     */
    async getRecentMovies(limit = 10) {
        return this.get('/movies/recent', { limit });
    }

    /**
     * Obtém filmes populares
     */
    async getPopularMovies(limit = 10) {
        return this.get('/movies/popular', { limit });
    }

    // ===== TMDB API =====

    /**
     * Busca filmes no TMDb
     */
    async searchTmdb(query, page = 1) {
        return this.get('/tmdb/search', { q: query, page });
    }

    /**
     * Obtém detalhes de filme do TMDb
     */
    async getTmdbMovie(id) {
        return this.get(`/tmdb/movie/${id}`);
    }

    /**
     * Obtém filmes populares do TMDb
     */
    async getTmdbPopular(page = 1) {
        return this.get('/tmdb/popular', { page });
    }

    /**
     * Obtém filmes em cartaz do TMDb
     */
    async getTmdbNowPlaying(page = 1) {
        return this.get('/tmdb/now-playing', { page });
    }

    /**
     * Obtém próximos lançamentos do TMDb
     */
    async getTmdbUpcoming(page = 1) {
        return this.get('/tmdb/upcoming', { page });
    }

    /**
     * Obtém filmes mais bem avaliados do TMDb
     */
    async getTmdbTopRated(page = 1) {
        return this.get('/tmdb/top-rated', { page });
    }

    /**
     * Obtém gêneros do TMDb
     */
    async getTmdbGenres() {
        return this.get('/tmdb/genres');
    }

    /**
     * Descobre filmes no TMDb
     */
    async discoverTmdb(filters = {}) {
        return this.get('/tmdb/discover', filters);
    }

    // ===== LINKS API =====

    /**
     * Lista links
     */
    async getLinks(params = {}) {
        return this.get('/links', params);
    }

    /**
     * Obtém detalhes de um link
     */
    async getLink(id) {
        return this.get(`/links/${id}`);
    }

    /**
     * Atualiza link
     */
    async updateLink(id, data) {
        return this.put(`/links/${id}`, data);
    }

    /**
     * Remove link
     */
    async deleteLink(id) {
        return this.delete(`/links/${id}`);
    }

    /**
     * Valida link
     */
    async validateLink(id) {
        return this.post(`/links/${id}/validate`);
    }

    /**
     * Valida múltiplos links
     */
    async validateBatchLinks(linkIds) {
        return this.post('/links/validate-batch', { linkIds });
    }

    /**
     * Obtém URL para streaming
     */
    async getStreamUrl(id) {
        return this.post(`/links/${id}/stream`);
    }

    /**
     * Obtém URL para download
     */
    async getDownloadUrl(id) {
        return this.post(`/links/${id}/download`);
    }

    /**
     * Obtém estatísticas de resolução
     */
    async getResolutionStats() {
        return this.get('/links/stats/resolution');
    }

    /**
     * Obtém estatísticas de provedor
     */
    async getProviderStats() {
        return this.get('/links/stats/provider');
    }

    // ===== HEALTH API =====

    /**
     * Health check
     */
    async getHealth() {
        return this.get('/health');
    }

    /**
     * Health check detalhado
     */
    async getDetailedHealth() {
        return this.get('/health/detailed');
    }

    /**
     * Obtém estatísticas do sistema
     */
    async getSystemStats() {
        return this.get('/health/stats');
    }

    /**
     * Executa limpeza do sistema
     */
    async cleanup() {
        return this.post('/health/cleanup');
    }
}

// Instância global da API
window.api = new ApiClient();
