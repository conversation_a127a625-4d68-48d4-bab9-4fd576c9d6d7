/**
 * API Client - Comunicação com o backend
 */

class ApiClient {
    constructor() {
        this.baseUrl = '';
        this.timeout = 30000;
    }

    /**
     * Faz requisição HTTP
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Adicionar timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        config.signal = controller.signal;

        try {
            const response = await fetch(url, config);
            clearTimeout(timeoutId);

            if (!response.ok) {
                const error = await response.json().catch(() => ({ error: 'Erro desconhecido' }));
                throw new Error(error.error || `HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('Timeout na requisição');
            }
            throw error;
        }
    }

    /**
     * GET request
     */
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== '') {
                url.searchParams.append(key, params[key]);
            }
        });

        const fullPath = url.pathname + url.search;
        return this.request(fullPath);
    }

    /**
     * POST request
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT request
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE request
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    // ===== MOVIES API =====

    /**
     * Lista filmes do catálogo
     */
    async getMovies(params = {}) {
        return this.get('/api/movies', params);
    }

    /**
     * Obtém detalhes de um filme
     */
    async getMovie(id) {
        return this.get(`/api/movies/${id}`);
    }

    /**
     * Cria novo filme
     */
    async createMovie(data) {
        return this.post('/api/movies', data);
    }

    /**
     * Atualiza filme
     */
    async updateMovie(id, data) {
        return this.put(`/api/movies/${id}`, data);
    }

    /**
     * Remove filme
     */
    async deleteMovie(id) {
        return this.delete(`/api/movies/${id}`);
    }

    /**
     * Obtém filmes recentes
     */
    async getRecentMovies(limit = 10) {
        return this.get('/api/movies/recent', { limit });
    }

    /**
     * Obtém filmes populares
     */
    async getPopularMovies(limit = 10) {
        return this.get('/api/movies/popular', { limit });
    }

    // ===== TMDB API =====

    /**
     * Busca filmes no TMDb
     */
    async searchTmdb(query, page = 1) {
        return this.get('/api/tmdb/search', { q: query, page });
    }

    /**
     * Obtém detalhes de filme do TMDb
     */
    async getTmdbMovie(id) {
        return this.get(`/api/tmdb/movie/${id}`);
    }

    /**
     * Obtém filmes populares do TMDb
     */
    async getTmdbPopular(page = 1) {
        return this.get('/api/tmdb/popular', { page });
    }

    /**
     * Obtém filmes em cartaz do TMDb
     */
    async getTmdbNowPlaying(page = 1) {
        return this.get('/api/tmdb/now-playing', { page });
    }

    /**
     * Obtém próximos lançamentos do TMDb
     */
    async getTmdbUpcoming(page = 1) {
        return this.get('/api/tmdb/upcoming', { page });
    }

    /**
     * Obtém filmes mais bem avaliados do TMDb
     */
    async getTmdbTopRated(page = 1) {
        return this.get('/api/tmdb/top-rated', { page });
    }

    /**
     * Obtém gêneros do TMDb
     */
    async getTmdbGenres() {
        return this.get('/api/tmdb/genres');
    }

    /**
     * Descobre filmes no TMDb
     */
    async discoverTmdb(filters = {}) {
        return this.get('/api/tmdb/discover', filters);
    }

    // ===== LINKS API =====

    /**
     * Lista links
     */
    async getLinks(params = {}) {
        return this.get('/api/links', params);
    }

    /**
     * Obtém detalhes de um link
     */
    async getLink(id) {
        return this.get(`/api/links/${id}`);
    }

    /**
     * Atualiza link
     */
    async updateLink(id, data) {
        return this.put(`/api/links/${id}`, data);
    }

    /**
     * Remove link
     */
    async deleteLink(id) {
        return this.delete(`/api/links/${id}`);
    }

    /**
     * Valida link
     */
    async validateLink(id) {
        return this.post(`/api/links/${id}/validate`);
    }

    /**
     * Valida múltiplos links
     */
    async validateBatchLinks(linkIds) {
        return this.post('/api/links/validate-batch', { linkIds });
    }

    /**
     * Obtém URL para streaming
     */
    async getStreamUrl(id) {
        return this.post(`/api/links/${id}/stream`);
    }

    /**
     * Obtém URL para download
     */
    async getDownloadUrl(id) {
        return this.post(`/api/links/${id}/download`);
    }

    /**
     * Obtém estatísticas de resolução
     */
    async getResolutionStats() {
        return this.get('/api/links/stats/resolution');
    }

    /**
     * Obtém estatísticas de provedor
     */
    async getProviderStats() {
        return this.get('/api/links/stats/provider');
    }

    // ===== HEALTH API =====

    /**
     * Health check
     */
    async getHealth() {
        return this.get('/api/health');
    }

    /**
     * Health check detalhado
     */
    async getDetailedHealth() {
        return this.get('/api/health/detailed');
    }

    /**
     * Obtém estatísticas do sistema
     */
    async getSystemStats() {
        return this.get('/api/health/stats');
    }

    /**
     * Executa limpeza do sistema
     */
    async cleanup() {
        return this.post('/api/health/cleanup');
    }
}

// Instância global da API
window.api = new ApiClient();
