/**
 * Serviço de Cache para Streaming
 */

const logger = require('../utils/logger');

class CacheService {
    constructor() {
        this.cache = new Map();
        this.defaultTTL = 30 * 60 * 1000; // 30 minutos
        this.maxSize = 1000; // Máximo de 1000 entradas
        
        // Limpeza automática a cada 10 minutos
        setInterval(() => this.cleanup(), 10 * 60 * 1000);
        
        logger.info('Cache Service inicializado');
    }

    /**
     * Gera chave de cache
     */
    generateKey(type, identifier, options = {}) {
        const optionsStr = Object.keys(options)
            .sort()
            .map(key => `${key}:${options[key]}`)
            .join('|');
        
        return `${type}:${identifier}${optionsStr ? ':' + optionsStr : ''}`;
    }

    /**
     * Armazena dados no cache
     */
    set(key, data, ttl = null) {
        try {
            // Verificar tamanho máximo
            if (this.cache.size >= this.maxSize) {
                this.evictOldest();
            }

            const expiresAt = Date.now() + (ttl || this.defaultTTL);
            
            this.cache.set(key, {
                data: data,
                createdAt: Date.now(),
                expiresAt: expiresAt,
                hits: 0
            });

            logger.debug(`Cache SET: ${key} (TTL: ${ttl || this.defaultTTL}ms)`);
            return true;
        } catch (error) {
            logger.error('Erro ao armazenar no cache:', error.message);
            return false;
        }
    }

    /**
     * Recupera dados do cache
     */
    get(key) {
        try {
            const entry = this.cache.get(key);
            
            if (!entry) {
                logger.debug(`Cache MISS: ${key}`);
                return null;
            }

            // Verificar expiração
            if (Date.now() > entry.expiresAt) {
                this.cache.delete(key);
                logger.debug(`Cache EXPIRED: ${key}`);
                return null;
            }

            // Incrementar contador de hits
            entry.hits++;
            
            logger.debug(`Cache HIT: ${key} (hits: ${entry.hits})`);
            return entry.data;
        } catch (error) {
            logger.error('Erro ao recuperar do cache:', error.message);
            return null;
        }
    }

    /**
     * Verifica se existe no cache
     */
    has(key) {
        const entry = this.cache.get(key);
        if (!entry) return false;
        
        // Verificar expiração
        if (Date.now() > entry.expiresAt) {
            this.cache.delete(key);
            return false;
        }
        
        return true;
    }

    /**
     * Remove entrada do cache
     */
    delete(key) {
        const deleted = this.cache.delete(key);
        if (deleted) {
            logger.debug(`Cache DELETE: ${key}`);
        }
        return deleted;
    }

    /**
     * Limpa cache expirado
     */
    cleanup() {
        const now = Date.now();
        let cleaned = 0;

        for (const [key, entry] of this.cache.entries()) {
            if (now > entry.expiresAt) {
                this.cache.delete(key);
                cleaned++;
            }
        }

        if (cleaned > 0) {
            logger.info(`Cache cleanup: ${cleaned} entradas removidas`);
        }
    }

    /**
     * Remove entrada mais antiga (LRU)
     */
    evictOldest() {
        let oldestKey = null;
        let oldestTime = Date.now();

        for (const [key, entry] of this.cache.entries()) {
            if (entry.createdAt < oldestTime) {
                oldestTime = entry.createdAt;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.cache.delete(oldestKey);
            logger.debug(`Cache EVICT: ${oldestKey}`);
        }
    }

    /**
     * Limpa todo o cache
     */
    clear() {
        const size = this.cache.size;
        this.cache.clear();
        logger.info(`Cache cleared: ${size} entradas removidas`);
    }

    /**
     * Estatísticas do cache
     */
    getStats() {
        const entries = Array.from(this.cache.values());
        const now = Date.now();
        
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            expired: entries.filter(e => now > e.expiresAt).length,
            totalHits: entries.reduce((sum, e) => sum + e.hits, 0),
            averageAge: entries.length > 0 ? 
                entries.reduce((sum, e) => sum + (now - e.createdAt), 0) / entries.length : 0,
            memoryUsage: this.estimateMemoryUsage()
        };
    }

    /**
     * Estima uso de memória (aproximado)
     */
    estimateMemoryUsage() {
        let size = 0;
        for (const [key, entry] of this.cache.entries()) {
            size += key.length * 2; // String UTF-16
            size += JSON.stringify(entry.data).length * 2;
            size += 64; // Overhead do objeto
        }
        return size;
    }

    /**
     * Cache específico para streaming
     */
    setStreamingOptions(movieId, options, subtitles = {}) {
        const key = this.generateKey('streaming', movieId);
        const data = {
            options: options,
            subtitles: subtitles,
            timestamp: Date.now()
        };
        
        // Cache por 1 hora para streaming
        return this.set(key, data, 60 * 60 * 1000);
    }

    /**
     * Recupera opções de streaming do cache
     */
    getStreamingOptions(movieId) {
        const key = this.generateKey('streaming', movieId);
        return this.get(key);
    }

    /**
     * Cache para legendas
     */
    setSubtitles(movieId, language, subtitles) {
        const key = this.generateKey('subtitles', movieId, { language });
        
        // Cache por 24 horas para legendas
        return this.set(key, subtitles, 24 * 60 * 60 * 1000);
    }

    /**
     * Recupera legendas do cache
     */
    getSubtitles(movieId, language) {
        const key = this.generateKey('subtitles', movieId, { language });
        return this.get(key);
    }

    /**
     * Cache para metadados de filme
     */
    setMovieMetadata(movieId, metadata) {
        const key = this.generateKey('movie', movieId);
        
        // Cache por 6 horas para metadados
        return this.set(key, metadata, 6 * 60 * 60 * 1000);
    }

    /**
     * Recupera metadados do cache
     */
    getMovieMetadata(movieId) {
        const key = this.generateKey('movie', movieId);
        return this.get(key);
    }
}

// Singleton
const cacheService = new CacheService();

module.exports = cacheService;
