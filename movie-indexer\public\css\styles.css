/* ===== RESET E VARIÁVEIS ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Cores principais */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-light: #dbeafe;
    --secondary-color: #6b7280;
    --accent-color: #f59e0b;
    
    /* Cores de fundo */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-modal: rgba(15, 23, 42, 0.95);
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* Cores de texto */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #1e293b;
    
    /* Cores de status */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* <PERSON><PERSON><PERSON> e sombras */
    --border-color: #334155;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Transições */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Espaçamentos */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Tipografia */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Layout */
    --container-max-width: 1400px;
    --header-height: 80px;
    --sidebar-width: 280px;
}

/* Tema claro */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --bg-card: #ffffff;
    --bg-modal: rgba(255, 255, 255, 0.95);
    
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-inverse: #ffffff;
    
    --border-color: #e2e8f0;
}

/* ===== BASE STYLES ===== */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* ===== CONTAINER ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* ===== HEADER ===== */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
    height: var(--header-height);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    gap: var(--spacing-xl);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-color);
    text-decoration: none;
}

.logo i {
    font-size: var(--font-size-2xl);
}

.logo h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== NAVIGATION ===== */
.nav {
    display: flex;
    gap: var(--spacing-xs);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
}

.nav-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.nav-btn.active {
    background: var(--primary-color);
    color: white;
}

.nav-btn i {
    font-size: var(--font-size-base);
}

/* ===== HEADER ACTIONS ===== */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: var(--bg-tertiary);
    border: none;
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* ===== MAIN CONTENT ===== */
.main {
    min-height: calc(100vh - var(--header-height));
    padding: var(--spacing-xl) 0;
}

/* ===== TAB CONTENT ===== */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== SECTION HEADER ===== */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.section-header h2 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
    font-weight: 700;
}

.section-header i {
    color: var(--primary-color);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* ===== BUTTONS ===== */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-error {
    background: var(--error-color);
    color: white;
}

/* ===== FILTERS ===== */
.filters {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-select, .search-input {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-fast);
    min-width: 150px;
}

.filter-select:focus, .search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input {
    min-width: 200px;
}

.search-input::placeholder {
    color: var(--text-muted);
}

/* ===== SEARCH SECTION ===== */
.search-section {
    max-width: 800px;
    margin: 0 auto var(--spacing-2xl);
}

.search-bar {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.search-input-main {
    flex: 1;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.search-input-main:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-filters {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== STATS BAR ===== */
.stats-bar {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* ===== TAB BUTTONS ===== */
.tab-buttons {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--bg-secondary);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.tab-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

/* ===== MOVIES GRID ===== */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.movie-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.movie-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.movie-poster {
    position: relative;
    aspect-ratio: 2/3;
    overflow: hidden;
    background: var(--bg-tertiary);
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.movie-card:hover .movie-poster img {
    transform: scale(1.05);
}

.movie-rating {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.8);
    color: var(--accent-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    backdrop-filter: blur(4px);
}

.movie-info {
    padding: var(--spacing-md);
}

.movie-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.movie-year {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.movie-genres {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.genre-tag {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.movie-synopsis {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.movie-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.movie-actions .btn-primary,
.movie-actions .btn-secondary {
    flex: 1;
    justify-content: center;
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* ===== LINKS SECTION ===== */
.links-filters {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.links-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.link-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: all var(--transition-fast);
}

.link-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.link-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.link-title {
    font-weight: 600;
    color: var(--text-primary);
}

.link-status {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.link-status.verified {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.link-status.unverified {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.link-status.invalid {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

.link-meta {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
}

.link-meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.link-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.link-actions .btn-primary,
.link-actions .btn-secondary {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* ===== PLACEHOLDERS ===== */
.loading-placeholder,
.welcome-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-muted);
}

.loading-placeholder i,
.welcome-message i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
}

.loading-placeholder h3,
.welcome-message h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-xl);
}

.loading-placeholder p,
.welcome-message p {
    font-size: var(--font-size-base);
}

/* ===== PAGINATION ===== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0 var(--spacing-md);
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
    box-shadow: var(--shadow-xl);
}

@keyframes modalSlideIn {
    from { transform: translateY(-20px) scale(0.95); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

/* ===== MOVIE DETAILS MODAL ===== */
.movie-details {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-xl);
    max-width: 900px;
}

.movie-details-poster {
    aspect-ratio: 2/3;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-tertiary);
}

.movie-details-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.movie-details-info h1 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-2xl);
    font-weight: 700;
}

.movie-details-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.movie-details-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.movie-details-synopsis {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.movie-details-cast {
    margin-bottom: var(--spacing-lg);
}

.movie-details-cast h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.cast-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.cast-member {
    background: var(--bg-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.movie-details-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.movie-links-section {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.movie-links-section h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.movie-links-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.movie-link-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.movie-link-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.movie-link-provider {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.movie-link-resolution {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
}

.movie-link-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.movie-link-actions .btn-primary,
.movie-link-actions .btn-secondary {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* ===== FORM STYLES ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-fast);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.8);
    z-index: 2000;
    backdrop-filter: blur(3px);
}

.loading-overlay.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.loading-spinner p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    min-width: 300px;
    max-width: 400px;
    box-shadow: var(--shadow-lg);
    animation: toastSlideIn 0.3s ease;
}

@keyframes toastSlideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.toast-close:hover {
    background: var(--bg-tertiary);
}

.toast-message {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }

    .movie-details {
        grid-template-columns: 250px 1fr;
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md) 0;
    }

    .nav {
        order: 2;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .header-actions {
        order: 1;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .section-actions {
        justify-content: stretch;
    }

    .filters {
        justify-content: stretch;
    }

    .filter-select,
    .search-input {
        flex: 1;
        min-width: auto;
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-sm);
    }

    .movie-details {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-md);
    }

    .movie-details-poster {
        max-width: 300px;
        margin: 0 auto;
    }

    .modal-content {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .form-actions {
        flex-direction: column;
    }

    .toast {
        min-width: auto;
        margin: 0 var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .stats-bar {
        flex-wrap: wrap;
        justify-content: center;
    }

    .search-bar {
        flex-direction: column;
    }

    .search-filters {
        flex-direction: column;
    }

    .links-filters {
        flex-direction: column;
    }

    .tab-buttons {
        flex-wrap: wrap;
    }

    .movie-actions {
        flex-direction: column;
    }

    .link-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .link-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .link-actions {
        flex-direction: column;
    }

    .movie-link-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .movie-link-actions {
        justify-content: stretch;
    }

    .movie-link-actions .btn-primary,
    .movie-link-actions .btn-secondary {
        flex: 1;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .main {
        padding: var(--spacing-md) 0;
    }

    .nav {
        flex-direction: column;
        width: 100%;
    }

    .nav-btn {
        justify-content: center;
        width: 100%;
    }

    .movies-grid {
        grid-template-columns: 1fr;
    }

    .section-header h2 {
        font-size: var(--font-size-xl);
    }

    .modal-body {
        padding: var(--spacing-md);
    }

    .modal-header {
        padding: var(--spacing-md);
    }

    .pagination {
        flex-wrap: wrap;
        gap: var(--spacing-xs);
    }

    .pagination-btn {
        width: 36px;
        height: 36px;
    }

    .stats-bar {
        flex-direction: column;
        text-align: center;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border-radius { border-radius: var(--border-radius); }
.border-radius-sm { border-radius: var(--border-radius-sm); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-spin { animation: spin 1s linear infinite; }
.animate-bounce { animation: bounce 1s infinite; }

/* ===== PRINT STYLES ===== */
@media print {
    .header,
    .modal,
    .toast-container,
    .loading-overlay,
    .btn-primary,
    .btn-secondary,
    .btn-icon,
    .pagination {
        display: none !important;
    }

    .main {
        padding: 0;
    }

    .movies-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-sm);
    }

    .movie-card {
        break-inside: avoid;
        border: 1px solid #ccc;
    }

    body {
        background: white;
        color: black;
    }
}
