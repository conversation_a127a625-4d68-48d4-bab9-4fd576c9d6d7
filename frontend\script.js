/**
 * Movie Stash - Frontend JavaScript
 * Gerencia toda a interface do usuário e comunicação com o backend
 */

class MovieStashUI {
    constructor() {
        this.currentTab = 'search';
        this.currentMovieId = null;
        this.searchResults = [];
        this.catalogMovies = [];
        
        this.init();
    }

    /**
     * Inicializa a aplicação
     */
    async init() {
        console.log('🎬 Inicializando Movie Stash UI...');
        
        this.setupEventListeners();
        this.setupTabNavigation();
        await this.loadInitialData();
        
        console.log('✅ Movie Stash UI inicializado!');
    }

    /**
     * Configura todos os event listeners
     */
    setupEventListeners() {
        // Busca de filmes
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput && searchBtn) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchMovies();
                }
            });
            
            searchBtn.addEventListener('click', () => {
                this.searchMovies();
            });
        }

        // Filtros do catálogo
        const statusFilter = document.getElementById('statusFilter');
        const catalogSearch = document.getElementById('catalogSearch');
        
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filterCatalog();
            });
        }
        
        if (catalogSearch) {
            catalogSearch.addEventListener('input', movieStashAPI.debounce(() => {
                this.filterCatalog();
            }, 300));
        }

        // Botões do header
        const openDownloadsFolder = document.getElementById('openDownloadsFolder');
        if (openDownloadsFolder) {
            openDownloadsFolder.addEventListener('click', () => {
                this.openDownloadsFolder();
            });
        }

        // Modais
        const closeModal = document.getElementById('closeModal');
        const closeDownloadModal = document.getElementById('closeDownloadModal');
        
        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.closeModal('movieModal');
            });
        }
        
        if (closeDownloadModal) {
            closeDownloadModal.addEventListener('click', () => {
                this.closeModal('downloadModal');
            });
        }

        // Formulário de download
        const downloadForm = document.getElementById('downloadForm');
        if (downloadForm) {
            downloadForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleDownloadSubmit();
            });
        }

        // Configurações
        const selectDownloadFolder = document.getElementById('selectDownloadFolder');
        if (selectDownloadFolder) {
            selectDownloadFolder.addEventListener('click', () => {
                this.selectDownloadFolder();
            });
        }

        // Fechar modal clicando fora
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    /**
     * Configura navegação entre abas
     */
    setupTabNavigation() {
        const navTabs = document.querySelectorAll('.nav-tab');
        const tabContents = document.querySelectorAll('.tab-content');

        navTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;
                
                // Remove active de todas as abas
                navTabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Ativa aba selecionada
                tab.classList.add('active');
                document.getElementById(`${targetTab}-tab`).classList.add('active');
                
                this.currentTab = targetTab;
                
                // Carrega dados específicos da aba
                this.handleTabChange(targetTab);
            });
        });
    }

    /**
     * Carrega dados iniciais
     */
    async loadInitialData() {
        await this.updateStats();
        await this.loadCatalog();
    }

    /**
     * Manipula mudança de aba
     */
    async handleTabChange(tab) {
        switch (tab) {
            case 'catalog':
                await this.loadCatalog();
                break;
            case 'downloads':
                await this.loadDownloads();
                break;
            case 'settings':
                await this.loadSettings();
                break;
        }
    }

    /**
     * Busca filmes no TMDb
     */
    async searchMovies() {
        const searchInput = document.getElementById('searchInput');
        const query = searchInput.value.trim();
        
        if (!query) {
            this.showToast('Aviso', 'Digite o nome de um filme para buscar', 'warning');
            return;
        }

        this.showLoading('Buscando filmes...');
        
        try {
            const result = await movieStashAPI.searchMovies(query);
            
            if (result.success) {
                this.searchResults = result.data;
                this.displaySearchResults(result.data);
            } else {
                this.showToast('Erro', result.error || 'Erro ao buscar filmes', 'error');
                this.displaySearchResults([]);
            }
        } catch (error) {
            console.error('Erro na busca:', error);
            this.showToast('Erro', 'Erro ao buscar filmes', 'error');
            this.displaySearchResults([]);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Exibe resultados da busca
     */
    displaySearchResults(movies) {
        const resultsContainer = document.getElementById('searchResults');
        
        if (!movies || movies.length === 0) {
            resultsContainer.innerHTML = `
                <div class="welcome-message">
                    <i class="fas fa-search"></i>
                    <h2>Nenhum filme encontrado</h2>
                    <p>Tente buscar com outro termo.</p>
                </div>
            `;
            return;
        }

        const moviesGrid = document.createElement('div');
        moviesGrid.className = 'movies-grid';
        
        movies.forEach(movie => {
            const movieCard = this.createMovieCard(movie, 'search');
            moviesGrid.appendChild(movieCard);
        });
        
        resultsContainer.innerHTML = '';
        resultsContainer.appendChild(moviesGrid);
    }

    /**
     * Cria card de filme
     */
    createMovieCard(movie, context = 'search') {
        const card = document.createElement('div');
        card.className = 'movie-card';
        
        const posterUrl = movie.poster || 'https://via.placeholder.com/300x450/334155/cbd5e1?text=Sem+Poster';
        const rating = movie.rating ? movie.rating.toFixed(1) : 'N/A';
        const genres = Array.isArray(movie.genre) ? movie.genre.slice(0, 3) : [];
        const synopsis = movieStashAPI.truncateText(movie.synopsis || 'Sinopse não disponível', 150);
        
        card.innerHTML = `
            <div class="movie-poster">
                <img src="${posterUrl}" alt="${movie.title}" loading="lazy">
                <div class="movie-rating">⭐ ${rating}</div>
            </div>
            <div class="movie-info">
                <h3 class="movie-title">${movie.title}</h3>
                <div class="movie-year">${movie.year || 'Ano desconhecido'}</div>
                <div class="movie-genres">
                    ${genres.map(genre => `<span class="genre-tag">${genre}</span>`).join('')}
                </div>
                <p class="movie-synopsis">${synopsis}</p>
            </div>
            <div class="movie-actions">
                ${this.getMovieActions(movie, context)}
            </div>
        `;
        
        // Event listeners para ações
        this.setupMovieCardEvents(card, movie, context);
        
        return card;
    }

    /**
     * Retorna HTML das ações do filme baseado no contexto
     */
    getMovieActions(movie, context) {
        if (context === 'search') {
            return `
                <button class="btn-secondary view-details" data-tmdb-id="${movie.tmdbId}">
                    <i class="fas fa-info-circle"></i>
                    Detalhes
                </button>
                <button class="btn-primary add-to-catalog" data-tmdb-id="${movie.tmdbId}">
                    <i class="fas fa-plus"></i>
                    Adicionar
                </button>
            `;
        } else if (context === 'catalog') {
            const statusClass = `status-${movie.status}`;
            const statusIcon = movie.status === 'downloaded' ? 'check' : 
                             movie.status === 'downloading' ? 'download' : 'list';
            
            return `
                <div class="status-indicator ${statusClass}">
                    <i class="fas fa-${statusIcon}"></i>
                    ${movie.status === 'cataloged' ? 'Catalogado' : 
                      movie.status === 'downloading' ? 'Baixando' : 'Baixado'}
                </div>
                <button class="btn-secondary view-catalog-details" data-movie-id="${movie.id}">
                    <i class="fas fa-eye"></i>
                    Ver
                </button>
                <button class="btn-primary add-download" data-movie-id="${movie.id}">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            `;
        }
        
        return '';
    }

    /**
     * Configura eventos dos cards de filme
     */
    setupMovieCardEvents(card, movie, context) {
        // Botão de detalhes (busca)
        const viewDetailsBtn = card.querySelector('.view-details');
        if (viewDetailsBtn) {
            viewDetailsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showMovieDetails(movie.tmdbId);
            });
        }

        // Botão adicionar ao catálogo
        const addToCatalogBtn = card.querySelector('.add-to-catalog');
        if (addToCatalogBtn) {
            addToCatalogBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.addMovieToCatalog(movie.tmdbId);
            });
        }

        // Botão ver detalhes do catálogo
        const viewCatalogDetailsBtn = card.querySelector('.view-catalog-details');
        if (viewCatalogDetailsBtn) {
            viewCatalogDetailsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showCatalogMovieDetails(movie.id);
            });
        }

        // Botão adicionar download
        const addDownloadBtn = card.querySelector('.add-download');
        if (addDownloadBtn) {
            addDownloadBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showDownloadModal(movie.id);
            });
        }

        // Clique no card para ver detalhes
        card.addEventListener('click', () => {
            if (context === 'search') {
                this.showMovieDetails(movie.tmdbId);
            } else if (context === 'catalog') {
                this.showCatalogMovieDetails(movie.id);
            }
        });
    }

    /**
     * Mostra detalhes do filme do TMDb
     */
    async showMovieDetails(tmdbId) {
        this.showLoading('Carregando detalhes...');
        
        try {
            const result = await movieStashAPI.getMovieDetails(tmdbId);
            
            if (result.success) {
                this.displayMovieDetailsModal(result.data, 'tmdb');
            } else {
                this.showToast('Erro', result.error || 'Erro ao carregar detalhes', 'error');
            }
        } catch (error) {
            console.error('Erro ao carregar detalhes:', error);
            this.showToast('Erro', 'Erro ao carregar detalhes do filme', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Adiciona filme ao catálogo
     */
    async addMovieToCatalog(tmdbId) {
        this.showLoading('Adicionando ao catálogo...');

        try {
            const result = await movieStashAPI.addToCatalog(tmdbId);

            if (result.success) {
                this.showToast('Sucesso', 'Filme adicionado ao catálogo!', 'success');
                await this.updateStats();

                // Se estiver na aba catálogo, recarrega
                if (this.currentTab === 'catalog') {
                    await this.loadCatalog();
                }
            } else {
                this.showToast('Erro', result.error || 'Erro ao adicionar filme', 'error');
            }
        } catch (error) {
            console.error('Erro ao adicionar filme:', error);
            this.showToast('Erro', 'Erro ao adicionar filme ao catálogo', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Carrega catálogo de filmes
     */
    async loadCatalog() {
        try {
            const result = await movieStashAPI.listCatalogMovies();

            if (result.success) {
                this.catalogMovies = result.data;
                this.displayCatalog(result.data);
            } else {
                this.showToast('Erro', result.error || 'Erro ao carregar catálogo', 'error');
                this.displayCatalog([]);
            }
        } catch (error) {
            console.error('Erro ao carregar catálogo:', error);
            this.displayCatalog([]);
        }
    }

    /**
     * Exibe catálogo de filmes
     */
    displayCatalog(movies) {
        const catalogGrid = document.getElementById('catalogGrid');

        if (!movies || movies.length === 0) {
            catalogGrid.innerHTML = `
                <div class="empty-catalog">
                    <i class="fas fa-inbox"></i>
                    <h3>Catálogo vazio</h3>
                    <p>Adicione filmes usando a aba "Buscar".</p>
                </div>
            `;
            return;
        }

        catalogGrid.innerHTML = '';

        movies.forEach(movie => {
            const movieCard = this.createMovieCard(movie, 'catalog');
            catalogGrid.appendChild(movieCard);
        });
    }

    /**
     * Filtra catálogo
     */
    async filterCatalog() {
        const statusFilter = document.getElementById('statusFilter').value;
        const searchTerm = document.getElementById('catalogSearch').value.trim();

        const filters = {};
        if (statusFilter) filters.status = statusFilter;
        if (searchTerm) filters.search = searchTerm;

        try {
            const result = await movieStashAPI.listCatalogMovies(filters);

            if (result.success) {
                this.displayCatalog(result.data);
            } else {
                this.displayCatalog([]);
            }
        } catch (error) {
            console.error('Erro ao filtrar catálogo:', error);
            this.displayCatalog([]);
        }
    }

    /**
     * Mostra detalhes do filme do catálogo
     */
    async showCatalogMovieDetails(movieId) {
        this.showLoading('Carregando detalhes...');

        try {
            const result = await movieStashAPI.getCatalogMovie(movieId);

            if (result.success) {
                this.displayMovieDetailsModal(result.data, 'catalog');
            } else {
                this.showToast('Erro', result.error || 'Erro ao carregar detalhes', 'error');
            }
        } catch (error) {
            console.error('Erro ao carregar detalhes:', error);
            this.showToast('Erro', 'Erro ao carregar detalhes do filme', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Exibe modal com detalhes do filme
     */
    displayMovieDetailsModal(movie, context) {
        const modal = document.getElementById('movieModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        modalTitle.textContent = movie.title;

        const posterUrl = movie.poster || 'https://via.placeholder.com/300x450/334155/cbd5e1?text=Sem+Poster';
        const rating = movie.rating ? movie.rating.toFixed(1) : 'N/A';
        const runtime = movie.runtime ? movieStashAPI.formatDuration(movie.runtime * 60) : 'N/A';
        const genres = Array.isArray(movie.genre) ? movie.genre : [];
        const cast = Array.isArray(movie.cast) ? movie.cast.slice(0, 5) : [];

        modalBody.innerHTML = `
            <div class="movie-details">
                <div class="movie-details-poster">
                    <img src="${posterUrl}" alt="${movie.title}">
                </div>
                <div class="movie-details-info">
                    <h1>${movie.title}</h1>
                    <div class="movie-details-meta">
                        <span><i class="fas fa-calendar"></i> ${movie.year || 'N/A'}</span>
                        <span><i class="fas fa-star"></i> ${rating}/10</span>
                        <span><i class="fas fa-clock"></i> ${runtime}</span>
                        ${movie.director ? `<span><i class="fas fa-user-tie"></i> ${movie.director}</span>` : ''}
                    </div>

                    ${genres.length > 0 ? `
                        <div class="movie-genres">
                            ${genres.map(genre => `<span class="genre-tag">${genre}</span>`).join('')}
                        </div>
                    ` : ''}

                    <div class="movie-details-synopsis">
                        <p>${movie.synopsis || 'Sinopse não disponível.'}</p>
                    </div>

                    ${cast.length > 0 ? `
                        <div class="movie-details-cast">
                            <h3>Elenco Principal</h3>
                            <div class="cast-list">
                                ${cast.map(actor => `<span class="cast-member">${typeof actor === 'string' ? actor : actor.name}</span>`).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <div class="movie-details-actions">
                        ${this.getModalActions(movie, context)}
                    </div>
                </div>
            </div>
        `;

        // Setup event listeners para ações do modal
        this.setupModalEvents(movie, context);

        modal.classList.add('active');
    }

    /**
     * Retorna HTML das ações do modal
     */
    getModalActions(movie, context) {
        if (context === 'tmdb') {
            return `
                <button class="btn-primary modal-add-to-catalog" data-tmdb-id="${movie.tmdbId}">
                    <i class="fas fa-plus"></i>
                    Adicionar ao Catálogo
                </button>
            `;
        } else if (context === 'catalog') {
            const downloadedFiles = movie.downloadedFiles || [];
            const downloadLinks = movie.downloadLinks || [];

            return `
                <button class="btn-primary modal-add-download" data-movie-id="${movie.id}">
                    <i class="fas fa-download"></i>
                    Adicionar Download
                </button>
                ${downloadedFiles.length > 0 ? `
                    <button class="btn-success modal-view-files" data-movie-id="${movie.id}">
                        <i class="fas fa-file-video"></i>
                        Ver Arquivos (${downloadedFiles.length})
                    </button>
                ` : ''}
                <button class="btn-error modal-remove-movie" data-movie-id="${movie.id}">
                    <i class="fas fa-trash"></i>
                    Remover
                </button>
            `;
        }

        return '';
    }

    /**
     * Configura eventos do modal
     */
    setupModalEvents(movie, context) {
        // Adicionar ao catálogo (modal TMDb)
        const addToCatalogBtn = document.querySelector('.modal-add-to-catalog');
        if (addToCatalogBtn) {
            addToCatalogBtn.addEventListener('click', () => {
                this.closeModal('movieModal');
                this.addMovieToCatalog(movie.tmdbId);
            });
        }

        // Adicionar download (modal catálogo)
        const addDownloadBtn = document.querySelector('.modal-add-download');
        if (addDownloadBtn) {
            addDownloadBtn.addEventListener('click', () => {
                this.closeModal('movieModal');
                this.showDownloadModal(movie.id);
            });
        }

        // Ver arquivos
        const viewFilesBtn = document.querySelector('.modal-view-files');
        if (viewFilesBtn) {
            viewFilesBtn.addEventListener('click', () => {
                this.openDownloadsFolder();
            });
        }

        // Remover filme
        const removeMovieBtn = document.querySelector('.modal-remove-movie');
        if (removeMovieBtn) {
            removeMovieBtn.addEventListener('click', () => {
                this.removeMovieFromCatalog(movie.id);
            });
        }
    }

    /**
     * Remove filme do catálogo
     */
    async removeMovieFromCatalog(movieId) {
        if (!confirm('Tem certeza que deseja remover este filme do catálogo?')) {
            return;
        }

        this.showLoading('Removendo filme...');

        try {
            const result = await movieStashAPI.removeFromCatalog(movieId);

            if (result.success) {
                this.showToast('Sucesso', 'Filme removido do catálogo!', 'success');
                this.closeModal('movieModal');
                await this.updateStats();
                await this.loadCatalog();
            } else {
                this.showToast('Erro', result.error || 'Erro ao remover filme', 'error');
            }
        } catch (error) {
            console.error('Erro ao remover filme:', error);
            this.showToast('Erro', 'Erro ao remover filme do catálogo', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Mostra modal de download
     */
    showDownloadModal(movieId) {
        this.currentMovieId = movieId;
        const modal = document.getElementById('downloadModal');

        // Limpa formulário
        document.getElementById('downloadForm').reset();

        modal.classList.add('active');
    }

    /**
     * Manipula envio do formulário de download
     */
    async handleDownloadSubmit() {
        const url = document.getElementById('downloadUrl').value.trim();
        const quality = document.getElementById('downloadQuality').value;
        const notes = document.getElementById('downloadNotes').value.trim();

        if (!url) {
            this.showToast('Aviso', 'Digite uma URL para download', 'warning');
            return;
        }

        if (!movieStashAPI.isValidUrl(url)) {
            this.showToast('Aviso', 'URL inválida', 'warning');
            return;
        }

        this.closeModal('downloadModal');

        // Detecta tipo de link
        const linkType = movieStashAPI.detectLinkType(url);

        // Adiciona link ao filme
        await this.addDownloadLinkToMovie(this.currentMovieId, {
            url: url,
            type: linkType,
            quality: quality,
            notes: notes
        });

        // Inicia download baseado no tipo
        await this.startDownload(this.currentMovieId, url, linkType, { quality, notes });
    }

    /**
     * Adiciona link de download ao filme
     */
    async addDownloadLinkToMovie(movieId, linkData) {
        try {
            const result = await movieStashAPI.addDownloadLink(movieId, linkData);

            if (!result.success) {
                console.warn('Erro ao adicionar link:', result.error);
            }
        } catch (error) {
            console.error('Erro ao adicionar link:', error);
        }
    }

    /**
     * Inicia download baseado no tipo
     */
    async startDownload(movieId, url, type, options = {}) {
        this.showLoading(`Iniciando download via ${type}...`);

        try {
            let result;

            switch (type) {
                case 'http':
                    result = await movieStashAPI.downloadHttp(movieId, url);
                    break;
                case 'torrent':
                    result = await movieStashAPI.downloadTorrent(movieId, url);
                    break;
                case 'ytdlp':
                    result = await movieStashAPI.downloadYtDlp(movieId, url, options);
                    break;
                default:
                    // Tenta HTTP como fallback
                    result = await movieStashAPI.downloadHttp(movieId, url);
            }

            if (result.success) {
                this.showToast('Sucesso', 'Download iniciado com sucesso!', 'success');

                // Atualiza catálogo se estiver na aba
                if (this.currentTab === 'catalog') {
                    await this.loadCatalog();
                }
            } else {
                this.showToast('Erro', result.error || 'Erro ao iniciar download', 'error');
            }
        } catch (error) {
            console.error('Erro no download:', error);
            this.showToast('Erro', 'Erro ao iniciar download', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Carrega downloads ativos
     */
    async loadDownloads() {
        const downloadsList = document.getElementById('downloadsList');

        // Por enquanto, mostra mensagem vazia
        // Em uma implementação completa, isso consultaria downloads ativos
        downloadsList.innerHTML = `
            <div class="empty-downloads">
                <i class="fas fa-cloud-download-alt"></i>
                <h3>Nenhum download ativo</h3>
                <p>Os downloads aparecerão aqui quando iniciados.</p>
            </div>
        `;
    }

    /**
     * Carrega configurações
     */
    async loadSettings() {
        await this.updateStats();
    }

    /**
     * Atualiza estatísticas
     */
    async updateStats() {
        try {
            const result = await movieStashAPI.getCatalogStats();

            if (result.success) {
                const stats = result.data;

                // Atualiza contador no header
                const totalMoviesElement = document.getElementById('totalMovies');
                if (totalMoviesElement) {
                    totalMoviesElement.textContent = stats.totalMovies || 0;
                }

                // Atualiza grid de estatísticas na aba configurações
                this.updateStatsGrid(stats);
            }
        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
        }
    }

    /**
     * Atualiza grid de estatísticas
     */
    updateStatsGrid(stats) {
        const statsGrid = document.getElementById('statsGrid');
        if (!statsGrid) return;

        statsGrid.innerHTML = `
            <div class="stat-card">
                <span class="stat-value">${stats.totalMovies || 0}</span>
                <span class="stat-label">Total de Filmes</span>
            </div>
            <div class="stat-card">
                <span class="stat-value">${stats.downloadedMovies || 0}</span>
                <span class="stat-label">Filmes Baixados</span>
            </div>
            <div class="stat-card">
                <span class="stat-value">${stats.catalogedMovies || 0}</span>
                <span class="stat-label">Catalogados</span>
            </div>
            <div class="stat-card">
                <span class="stat-value">${stats.downloadingMovies || 0}</span>
                <span class="stat-label">Baixando</span>
            </div>
        `;
    }

    /**
     * Abre pasta de downloads
     */
    async openDownloadsFolder() {
        try {
            const result = await movieStashAPI.openDownloadsFolder();

            if (!result.success) {
                this.showToast('Erro', result.error || 'Erro ao abrir pasta', 'error');
            }
        } catch (error) {
            console.error('Erro ao abrir pasta:', error);
            this.showToast('Erro', 'Erro ao abrir pasta de downloads', 'error');
        }
    }

    /**
     * Seleciona pasta de download
     */
    async selectDownloadFolder() {
        try {
            const result = await movieStashAPI.selectDownloadFolder();

            if (result.success) {
                const pathInput = document.getElementById('downloadPath');
                if (pathInput) {
                    pathInput.value = result.data;
                }
                this.showToast('Sucesso', 'Pasta de download atualizada!', 'success');
            }
        } catch (error) {
            console.error('Erro ao selecionar pasta:', error);
            this.showToast('Erro', 'Erro ao selecionar pasta', 'error');
        }
    }

    // ===== UTILITY METHODS =====

    /**
     * Mostra overlay de loading
     */
    showLoading(message = 'Carregando...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');

        if (text) text.textContent = message;
        if (overlay) overlay.classList.add('active');
    }

    /**
     * Esconde overlay de loading
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) overlay.classList.remove('active');
    }

    /**
     * Mostra modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) modal.classList.add('active');
    }

    /**
     * Fecha modal
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) modal.classList.remove('active');
    }

    /**
     * Mostra notificação toast
     */
    showToast(title, message, type = 'info') {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const toastId = movieStashAPI.generateId();
        toast.id = toastId;

        toast.innerHTML = `
            <div class="toast-header">
                <span class="toast-title">${title}</span>
                <button class="toast-close" onclick="movieStashUI.removeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-message">${message}</div>
        `;

        container.appendChild(toast);

        // Remove automaticamente após 5 segundos
        setTimeout(() => {
            this.removeToast(toastId);
        }, 5000);
    }

    /**
     * Remove notificação toast
     */
    removeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.style.animation = 'toastSlideOut 0.3s ease forwards';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }
}

// ===== INICIALIZAÇÃO =====

// Instância global da UI
let movieStashUI;

// Inicializa quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', () => {
    movieStashUI = new MovieStashUI();
});

// Adiciona animação de saída para toast
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Expõe instância globalmente para debug
window.movieStashUI = movieStashUI;
