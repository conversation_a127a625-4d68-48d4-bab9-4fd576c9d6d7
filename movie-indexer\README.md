# 🎬 Movie Indexer

**Movie Indexer** é um sistema completo de indexação pessoal de filmes com backend Node.js e API REST. Permite catalogar filmes, adicionar links de download/streaming e validar se os links são públicos e acessíveis.

## ✨ Funcionalidades

### 🔍 **Catálogo Inteligente**
- **Busca integrada** com API do TMDb
- **Metadados completos**: título, ano, sinopse, gênero, capa, elenco, diretor
- **Catálogo local** armazenado em SQLite
- **Filtros avançados** por resolução, gênero, ano, status

### 🔗 **Gerenciamento de Links**
- **Múltiplos provedores**: HTTP, Google Drive, Mega, Archive.org, Dropbox, MediaFire
- **Validação automática** de links públicos
- **Múltiplas resoluções**: 480p, 720p, 1080p, 4K
- **Qualidades diferentes**: CAM, TS, DVDRip, BluRay, WebRip, Web-DL
- **Contadores** de downloads e streams

### 🎨 **Interface Moderna**
- **Design responsivo** com tema escuro/claro
- **Navegação por abas** intuitiva
- **Modais informativos** para detalhes
- **Notificações toast** em tempo real
- **Filtros dinâmicos** e busca instantânea

### 🛡️ **API REST Completa**
- **Endpoints RESTful** bem estruturados
- **Validação de dados** com express-validator
- **Rate limiting** e segurança
- **Logs detalhados** de atividades
- **Health checks** e monitoramento

## 🚀 Instalação

### Pré-requisitos

1. **Node.js** (versão 16 ou superior)
   ```bash
   node --version
   npm --version
   ```

2. **Git** (opcional, para clonar o repositório)

### Instalação do Projeto

1. **Clone ou baixe** este projeto
2. **Navegue** para a pasta do projeto:
   ```bash
   cd movie-indexer
   ```

3. **Instale as dependências**:
   ```bash
   npm install
   ```

4. **Configure as variáveis de ambiente**:
   ```bash
   cp .env.example .env
   ```
   
   Edite o arquivo `.env` se necessário (a configuração padrão já funciona).

5. **Execute o servidor**:
   ```bash
   npm start
   ```

6. **Acesse a aplicação**:
   - Frontend: http://localhost:3000
   - API: http://localhost:3000/api
   - Health: http://localhost:3000/api/health

## 📁 Estrutura do Projeto

```
movie-indexer/
├── 📁 src/                    # Código fonte do backend
│   ├── 📁 config/             # Configurações (banco, etc.)
│   ├── 📁 controllers/        # Controladores da API
│   ├── 📁 models/             # Modelos de dados
│   ├── 📁 services/           # Serviços (TMDb, validação)
│   ├── 📁 middleware/         # Middleware Express
│   ├── 📁 routes/             # Rotas da API REST
│   └── 📁 utils/              # Utilitários (logger, etc.)
├── 📁 public/                 # Frontend estático
│   ├── 📁 css/                # Estilos CSS
│   ├── 📁 js/                 # JavaScript do frontend
│   └── index.html             # Página principal
├── 📁 data/                   # Banco de dados SQLite
├── 📁 uploads/                # Uploads de arquivos
├── 📁 logs/                   # Logs da aplicação
├── 📁 scripts/                # Scripts utilitários
├── server.js                  # Servidor principal
├── package.json               # Dependências e scripts
└── README.md                  # Este arquivo
```

## 🎯 Como Usar

### 1. **Buscar Filmes**
- Vá para a aba **"Buscar"**
- Digite o nome do filme
- Clique em **"Buscar"** ou pressione Enter
- Navegue pelos resultados do TMDb

### 2. **Adicionar ao Catálogo**
- Clique em **"Detalhes"** para ver informações completas
- Clique em **"Adicionar"** para salvar no catálogo local
- O filme aparecerá na aba **"Catálogo"**

### 3. **Adicionar Links**
- Na aba **"Catálogo"**, clique em **"Link"** no filme desejado
- Cole o link de download/streaming
- Escolha a resolução e qualidade
- Clique em **"Adicionar Link"**

### 4. **Assistir/Baixar**
- Links verificados mostram botões **"Assistir"** e **"Baixar"**
- Clique para abrir o link em nova aba
- O sistema conta automaticamente acessos

### 5. **Gerenciar Links**
- Aba **"Links"** mostra todos os links cadastrados
- **Validar** links para verificar se estão funcionando
- **Filtrar** por provedor, resolução ou status
- **Remover** links quebrados

## 🔗 Tipos de Links Suportados

### HTTP/HTTPS
```
https://exemplo.com/filme.mp4
https://servidor.com/download/filme.mkv
```

### Google Drive
```
https://drive.google.com/file/d/1234567890/view?usp=sharing
https://docs.google.com/file/d/1234567890/edit
```

### Mega
```
https://mega.nz/file/abcdefgh#1234567890
https://mega.co.nz/#!abcdefgh!1234567890
```

### Archive.org
```
https://archive.org/download/filme/filme.mp4
```

### Dropbox
```
https://www.dropbox.com/s/1234567890/filme.mp4?dl=0
```

### MediaFire
```
https://www.mediafire.com/file/1234567890/filme.mp4/file
```

## ⚙️ API REST

### Endpoints Principais

#### Filmes
- `GET /api/movies` - Lista filmes do catálogo
- `GET /api/movies/:id` - Detalhes de um filme
- `POST /api/movies` - Adiciona filme ao catálogo
- `PUT /api/movies/:id` - Atualiza filme
- `DELETE /api/movies/:id` - Remove filme

#### TMDb
- `GET /api/tmdb/search?q=query` - Busca filmes
- `GET /api/tmdb/movie/:id` - Detalhes do TMDb
- `GET /api/tmdb/popular` - Filmes populares
- `GET /api/tmdb/now-playing` - Em cartaz
- `GET /api/tmdb/upcoming` - Próximos lançamentos

#### Links
- `GET /api/links` - Lista todos os links
- `POST /api/movies/:id/links` - Adiciona link a filme
- `POST /api/links/:id/validate` - Valida link
- `POST /api/links/:id/stream` - Obtém URL para streaming
- `POST /api/links/:id/download` - Obtém URL para download

#### Health
- `GET /api/health` - Status básico
- `GET /api/health/detailed` - Status detalhado
- `GET /api/health/stats` - Estatísticas do sistema

## 🛠️ Desenvolvimento

### Scripts Disponíveis
```bash
# Executar em modo desenvolvimento
npm run dev

# Executar normalmente
npm start

# Executar testes
npm test

# Popular banco com dados de exemplo
npm run seed
```

### Configuração do Ambiente

#### Variáveis do .env
```env
# Servidor
PORT=3000
NODE_ENV=development

# TMDb API
TMDB_API_KEY=545f2df151ee4d8c39eeb33245a600c0

# Banco de dados
DB_PATH=./data/movies.db

# Segurança
JWT_SECRET=movie-indexer-secret-key

# Rate limiting
RATE_LIMIT_MAX_REQUESTS=100
```

### Personalização

#### Modificar Tema
- Edite as variáveis CSS em `public/css/styles.css`
- Altere as cores em `:root { --primary-color: #3b82f6; }`

#### Adicionar Novos Provedores
1. Edite `src/services/linkValidator.js`
2. Adicione método de validação específico
3. Atualize `detectProvider()` e ícones no frontend

#### Modificar Layout
- Estrutura: `public/index.html`
- Estilos: `public/css/styles.css`
- Funcionalidade: `public/js/app.js`

## 🐛 Solução de Problemas

### Erro de conexão com TMDb
- Verifique sua conexão com a internet
- A chave da API pode ter expirado

### Banco de dados não inicializa
- Verifique permissões da pasta `./data/`
- Execute como administrador se necessário

### Links não validam
- Verifique se o link está correto
- Teste o link em um navegador primeiro
- Alguns provedores podem bloquear validação automática

### Interface não carrega
- Verifique se o servidor está rodando na porta 3000
- Limpe o cache do navegador (Ctrl+F5)

## 📝 Licença

Este projeto é licenciado sob a MIT License.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

---

**Movie Indexer** - Seu indexador pessoal de filmes! 🎬✨
