/**
 * Modelo de Histórico de Streaming
 */

const database = require('../config/database');
const logger = require('../utils/logger');

class StreamingHistory {
    constructor(data = {}) {
        this.id = data.id || null;
        this.movie_id = data.movie_id;
        this.title = data.title;
        this.quality = data.quality;
        this.provider = data.provider;
        this.magnet_link = data.magnet_link;
        this.duration_seconds = data.duration_seconds || 0;
        this.progress_percent = data.progress_percent || 0;
        this.completed = data.completed || false;
        this.started_at = data.started_at;
        this.last_watched_at = data.last_watched_at;
        this.ended_at = data.ended_at;
        this.user_rating = data.user_rating;
        this.user_notes = data.user_notes || '';
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
    }

    /**
     * Salva entrada no histórico
     */
    async save() {
        try {
            const now = new Date().toISOString();
            
            if (this.id) {
                // Atualizar existente
                const sql = `
                    UPDATE streaming_history SET
                        duration_seconds = ?,
                        progress_percent = ?,
                        completed = ?,
                        last_watched_at = ?,
                        ended_at = ?,
                        user_rating = ?,
                        user_notes = ?,
                        updated_at = ?
                    WHERE id = ?
                `;
                
                await database.run(sql, [
                    this.duration_seconds,
                    this.progress_percent,
                    this.completed,
                    this.last_watched_at || now,
                    this.ended_at,
                    this.user_rating,
                    this.user_notes,
                    now,
                    this.id
                ]);
                
                logger.debug(`Histórico de streaming atualizado: ${this.id}`);
            } else {
                // Criar novo
                const sql = `
                    INSERT INTO streaming_history (
                        movie_id, title, quality, provider, magnet_link,
                        duration_seconds, progress_percent, completed,
                        started_at, last_watched_at, user_notes, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;
                
                const result = await database.run(sql, [
                    this.movie_id,
                    this.title,
                    this.quality,
                    this.provider,
                    this.magnet_link,
                    this.duration_seconds,
                    this.progress_percent,
                    this.completed,
                    this.started_at || now,
                    this.last_watched_at || now,
                    this.user_notes,
                    now,
                    now
                ]);
                
                this.id = result.lastID;
                this.created_at = now;
                this.updated_at = now;
                
                logger.info(`Nova entrada no histórico: ${this.title} (${this.quality})`);
            }
            
            return this;
        } catch (error) {
            logger.error('Erro ao salvar histórico de streaming:', error.message);
            throw error;
        }
    }

    /**
     * Atualiza progresso do streaming
     */
    async updateProgress(progressPercent, durationSeconds = null) {
        try {
            this.progress_percent = progressPercent;
            this.last_watched_at = new Date().toISOString();
            
            if (durationSeconds !== null) {
                this.duration_seconds = durationSeconds;
            }
            
            // Marcar como completo se progresso >= 90%
            if (progressPercent >= 90 && !this.completed) {
                this.completed = true;
                this.ended_at = new Date().toISOString();
            }
            
            await this.save();
            return this;
        } catch (error) {
            logger.error('Erro ao atualizar progresso:', error.message);
            throw error;
        }
    }

    /**
     * Finaliza sessão de streaming
     */
    async endSession(userRating = null, userNotes = null) {
        try {
            this.ended_at = new Date().toISOString();
            
            if (userRating !== null) {
                this.user_rating = userRating;
            }
            
            if (userNotes !== null) {
                this.user_notes = userNotes;
            }
            
            await this.save();
            return this;
        } catch (error) {
            logger.error('Erro ao finalizar sessão:', error.message);
            throw error;
        }
    }

    /**
     * Busca histórico por ID
     */
    static async findById(id) {
        try {
            const sql = 'SELECT * FROM streaming_history WHERE id = ?';
            const row = await database.get(sql, [id]);
            
            return row ? new StreamingHistory(row) : null;
        } catch (error) {
            logger.error('Erro ao buscar histórico por ID:', error.message);
            throw error;
        }
    }

    /**
     * Busca histórico por filme
     */
    static async findByMovieId(movieId, limit = 10) {
        try {
            const sql = `
                SELECT * FROM streaming_history 
                WHERE movie_id = ? 
                ORDER BY last_watched_at DESC 
                LIMIT ?
            `;
            
            const rows = await database.all(sql, [movieId, limit]);
            return rows.map(row => new StreamingHistory(row));
        } catch (error) {
            logger.error('Erro ao buscar histórico por filme:', error.message);
            throw error;
        }
    }

    /**
     * Lista histórico recente
     */
    static async findRecent(limit = 20, offset = 0) {
        try {
            const sql = `
                SELECT sh.*, m.poster_path, m.year
                FROM streaming_history sh
                LEFT JOIN movies m ON sh.movie_id = m.id
                ORDER BY sh.last_watched_at DESC
                LIMIT ? OFFSET ?
            `;
            
            const rows = await database.all(sql, [limit, offset]);
            return rows.map(row => {
                const history = new StreamingHistory(row);
                history.poster_path = row.poster_path;
                history.year = row.year;
                return history;
            });
        } catch (error) {
            logger.error('Erro ao buscar histórico recente:', error.message);
            throw error;
        }
    }

    /**
     * Busca filmes mais assistidos
     */
    static async findMostWatched(limit = 10) {
        try {
            const sql = `
                SELECT 
                    sh.movie_id,
                    sh.title,
                    m.poster_path,
                    m.year,
                    COUNT(*) as watch_count,
                    AVG(sh.progress_percent) as avg_progress,
                    MAX(sh.last_watched_at) as last_watched
                FROM streaming_history sh
                LEFT JOIN movies m ON sh.movie_id = m.id
                GROUP BY sh.movie_id, sh.title
                ORDER BY watch_count DESC, last_watched DESC
                LIMIT ?
            `;
            
            const rows = await database.all(sql, [limit]);
            return rows;
        } catch (error) {
            logger.error('Erro ao buscar mais assistidos:', error.message);
            throw error;
        }
    }

    /**
     * Estatísticas do histórico
     */
    static async getStats() {
        try {
            const [totalSessions, completedSessions, totalTime, avgRating] = await Promise.all([
                database.get('SELECT COUNT(*) as count FROM streaming_history'),
                database.get('SELECT COUNT(*) as count FROM streaming_history WHERE completed = 1'),
                database.get('SELECT SUM(duration_seconds) as total FROM streaming_history'),
                database.get('SELECT AVG(user_rating) as avg FROM streaming_history WHERE user_rating IS NOT NULL')
            ]);

            return {
                totalSessions: totalSessions.count,
                completedSessions: completedSessions.count,
                completionRate: totalSessions.count > 0 ? 
                    (completedSessions.count / totalSessions.count * 100).toFixed(1) : 0,
                totalTimeHours: Math.round((totalTime.total || 0) / 3600),
                averageRating: avgRating.avg ? parseFloat(avgRating.avg).toFixed(1) : null
            };
        } catch (error) {
            logger.error('Erro ao obter estatísticas:', error.message);
            return {
                totalSessions: 0,
                completedSessions: 0,
                completionRate: 0,
                totalTimeHours: 0,
                averageRating: null
            };
        }
    }

    /**
     * Remove entradas antigas (limpeza)
     */
    static async cleanup(daysOld = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            
            const sql = 'DELETE FROM streaming_history WHERE created_at < ?';
            const result = await database.run(sql, [cutoffDate.toISOString()]);
            
            logger.info(`Limpeza do histórico: ${result.changes} entradas removidas`);
            return result.changes;
        } catch (error) {
            logger.error('Erro na limpeza do histórico:', error.message);
            throw error;
        }
    }
}

module.exports = StreamingHistory;
