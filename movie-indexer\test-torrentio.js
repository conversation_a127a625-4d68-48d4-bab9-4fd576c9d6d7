/**
 * Teste direto do Torrentio
 */

const TorrentioAddon = require('./src/addons/torrentio');

async function testTorrentio() {
    try {
        console.log('Testando Torrentio...');
        
        const torrentio = new TorrentioAddon();
        
        // Testar conexão
        console.log('1. Testando conexão...');
        const connection = await torrentio.testConnection();
        console.log('Conexão:', connection);
        
        // Testar busca por IMDB ID conhecido (Fight Club)
        console.log('\n2. Testando busca por IMDB ID (Fight Club - tt0137523)...');
        const results1 = await torrentio.searchByImdbId('tt0137523');
        console.log('Resultados por IMDB ID:', results1.length);
        if (results1.length > 0) {
            console.log('Primeiro resultado:', results1[0]);
        }
        
        // Testar busca por título
        console.log('\n3. Testando busca por título...');
        const results2 = await torrentio.searchByTitle('Fight Club', { year: 1999, tmdbId: 550 });
        console.log('Resultados por título:', results2.length);
        if (results2.length > 0) {
            console.log('Primeiro resultado:', results2[0]);
        }
        
        // Testar busca genérica
        console.log('\n4. Testando busca genérica...');
        const results3 = await torrentio.search('Fight Club', { year: 1999, tmdbId: 550 });
        console.log('Resultados genéricos:', results3.length);
        if (results3.length > 0) {
            console.log('Primeiro resultado:', results3[0]);
        }
        
    } catch (error) {
        console.error('Erro no teste:', error.message);
        console.error('Stack:', error.stack);
    }
}

testTorrentio();
