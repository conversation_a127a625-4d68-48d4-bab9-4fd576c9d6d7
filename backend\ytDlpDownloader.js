const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Classe para fazer download de vídeos usando yt-dlp
 */
class YtDlpDownloader {
    constructor() {
        this.downloadsPath = path.join(__dirname, '..', 'downloads');
        this.ytDlpPath = 'yt-dlp'; // Assume que yt-dlp está no PATH
        this.ensureDownloadsDirectory();
    }

    /**
     * Garante que a pasta de downloads existe
     */
    ensureDownloadsDirectory() {
        if (!fs.existsSync(this.downloadsPath)) {
            fs.mkdirSync(this.downloadsPath, { recursive: true });
            console.log(`📁 Pasta de downloads criada: ${this.downloadsPath}`);
        }
    }

    /**
     * Verifica se o yt-dlp está instalado
     * @returns {Promise<boolean>} True se yt-dlp estiver disponível
     */
    async checkYtDlpAvailable() {
        return new Promise((resolve) => {
            const process = spawn(this.ytDlpPath, ['--version'], { stdio: 'pipe' });
            
            process.on('close', (code) => {
                resolve(code === 0);
            });

            process.on('error', () => {
                resolve(false);
            });
        });
    }

    /**
     * Obtém informações sobre o vídeo sem baixar
     * @param {string} url - URL do vídeo
     * @returns {Promise<Object>} Informações do vídeo
     */
    async getVideoInfo(url) {
        return new Promise((resolve, reject) => {
            console.log(`ℹ️  Obtendo informações do vídeo: ${url}`);

            const args = [
                '--dump-json',
                '--no-playlist',
                url
            ];

            const process = spawn(this.ytDlpPath, args, { stdio: 'pipe' });
            let stdout = '';
            let stderr = '';

            process.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    try {
                        const videoInfo = JSON.parse(stdout);
                        const info = {
                            title: videoInfo.title,
                            duration: videoInfo.duration,
                            durationFormatted: this.formatDuration(videoInfo.duration),
                            uploader: videoInfo.uploader,
                            uploadDate: videoInfo.upload_date,
                            viewCount: videoInfo.view_count,
                            description: videoInfo.description,
                            thumbnail: videoInfo.thumbnail,
                            formats: videoInfo.formats?.map(f => ({
                                formatId: f.format_id,
                                ext: f.ext,
                                quality: f.quality,
                                resolution: f.resolution,
                                filesize: f.filesize,
                                filesizeFormatted: f.filesize ? this.formatBytes(f.filesize) : 'N/A'
                            })) || []
                        };
                        
                        console.log(`✅ Informações obtidas: ${info.title}`);
                        resolve(info);
                    } catch (error) {
                        console.error(`❌ Erro ao parsear informações do vídeo: ${error.message}`);
                        reject(new Error('Erro ao parsear informações do vídeo'));
                    }
                } else {
                    console.error(`❌ Erro ao obter informações: ${stderr}`);
                    reject(new Error(stderr || 'Erro desconhecido ao obter informações'));
                }
            });

            process.on('error', (error) => {
                console.error(`❌ Erro ao executar yt-dlp: ${error.message}`);
                reject(error);
            });
        });
    }

    /**
     * Faz download de um vídeo
     * @param {string} url - URL do vídeo
     * @param {Object} options - Opções de download
     * @returns {Promise<Object>} Resultado do download
     */
    async downloadVideo(url, options = {}) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log(`🎬 Iniciando download do vídeo: ${url}`);

                // Verifica se yt-dlp está disponível
                const isAvailable = await this.checkYtDlpAvailable();
                if (!isAvailable) {
                    throw new Error('yt-dlp não está instalado ou não está no PATH');
                }

                // Configurações padrão
                const defaultOptions = {
                    quality: 'best', // best, worst, bestvideo, bestaudio, 720p, 1080p, etc.
                    format: 'mp4',
                    audioOnly: false,
                    subtitles: false,
                    customFileName: null
                };

                const config = { ...defaultOptions, ...options };

                // Monta argumentos do yt-dlp
                const args = [
                    '--no-playlist',
                    '--output', path.join(this.downloadsPath, '%(title)s.%(ext)s'),
                    '--format', this.buildFormatString(config),
                    '--newline', // Para melhor parsing do progresso
                ];

                // Adiciona nome personalizado se especificado
                if (config.customFileName) {
                    args[3] = path.join(this.downloadsPath, `${config.customFileName}.%(ext)s`);
                }

                // Adiciona legendas se solicitado
                if (config.subtitles) {
                    args.push('--write-subs', '--write-auto-subs', '--sub-lang', 'pt,en');
                }

                // Adiciona URL
                args.push(url);

                console.log(`🔧 Executando: yt-dlp ${args.join(' ')}`);

                const process = spawn(this.ytDlpPath, args, { stdio: 'pipe' });
                let stderr = '';
                let downloadedFile = null;

                process.stdout.on('data', (data) => {
                    const output = data.toString();
                    
                    // Parse do progresso
                    const lines = output.split('\n');
                    lines.forEach(line => {
                        if (line.includes('[download]')) {
                            if (line.includes('Destination:')) {
                                // Extrai nome do arquivo de destino
                                const match = line.match(/Destination: (.+)/);
                                if (match) {
                                    downloadedFile = match[1].trim();
                                }
                            } else if (line.includes('%')) {
                                // Mostra progresso
                                process.stdout.write(`\r${line.trim()}`);
                            } else if (line.includes('has already been downloaded')) {
                                console.log(`\n⚠️  Arquivo já foi baixado anteriormente`);
                            }
                        } else if (line.includes('[info]')) {
                            console.log(`ℹ️  ${line.replace('[info]', '').trim()}`);
                        }
                    });
                });

                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                    // Mostra erros em tempo real
                    const lines = data.toString().split('\n');
                    lines.forEach(line => {
                        if (line.trim() && !line.includes('WARNING')) {
                            console.error(`⚠️  ${line.trim()}`);
                        }
                    });
                });

                process.on('close', (code) => {
                    console.log(''); // Nova linha após o progresso

                    if (code === 0) {
                        console.log(`✅ Download concluído com sucesso!`);
                        
                        // Tenta encontrar o arquivo baixado
                        if (!downloadedFile) {
                            // Se não conseguiu capturar o nome, procura o arquivo mais recente
                            const files = this.getRecentDownloads(1);
                            downloadedFile = files.length > 0 ? files[0].path : null;
                        }

                        if (downloadedFile && fs.existsSync(downloadedFile)) {
                            const stats = fs.statSync(downloadedFile);
                            console.log(`📁 Arquivo salvo: ${downloadedFile}`);
                            console.log(`📊 Tamanho: ${this.formatBytes(stats.size)}`);

                            resolve({
                                success: true,
                                filePath: downloadedFile,
                                fileName: path.basename(downloadedFile),
                                size: stats.size,
                                sizeFormatted: this.formatBytes(stats.size),
                                url: url
                            });
                        } else {
                            resolve({
                                success: true,
                                message: 'Download concluído, mas não foi possível localizar o arquivo',
                                url: url
                            });
                        }
                    } else {
                        console.error(`❌ Download falhou com código: ${code}`);
                        console.error(`❌ Erro: ${stderr}`);
                        reject(new Error(stderr || `Processo terminou com código ${code}`));
                    }
                });

                process.on('error', (error) => {
                    console.error(`❌ Erro ao executar yt-dlp: ${error.message}`);
                    reject(error);
                });

            } catch (error) {
                console.error(`❌ Erro no download: ${error.message}`);
                reject(error);
            }
        });
    }

    /**
     * Constrói string de formato para yt-dlp
     * @param {Object} config - Configurações
     * @returns {string} String de formato
     */
    buildFormatString(config) {
        if (config.audioOnly) {
            return 'bestaudio/best';
        }

        switch (config.quality) {
            case 'best':
                return 'best[ext=mp4]/best';
            case 'worst':
                return 'worst[ext=mp4]/worst';
            case '720p':
                return 'best[height<=720][ext=mp4]/best[height<=720]/best';
            case '1080p':
                return 'best[height<=1080][ext=mp4]/best[height<=1080]/best';
            case '480p':
                return 'best[height<=480][ext=mp4]/best[height<=480]/best';
            default:
                return config.quality;
        }
    }

    /**
     * Obtém downloads recentes
     * @param {number} limit - Número máximo de arquivos
     * @returns {Array} Lista de arquivos recentes
     */
    getRecentDownloads(limit = 10) {
        try {
            const files = fs.readdirSync(this.downloadsPath);
            const fileStats = files.map(file => {
                const filePath = path.join(this.downloadsPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    path: filePath,
                    size: stats.size,
                    sizeFormatted: this.formatBytes(stats.size),
                    created: stats.birthtime,
                    modified: stats.mtime
                };
            });

            // Ordena por data de criação (mais recente primeiro)
            fileStats.sort((a, b) => b.created - a.created);
            
            return fileStats.slice(0, limit);
        } catch (error) {
            console.error(`❌ Erro ao listar downloads: ${error.message}`);
            return [];
        }
    }

    /**
     * Converte bytes para formato legível
     * @param {number} bytes - Número de bytes
     * @returns {string} Tamanho formatado
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Formata duração em segundos para formato legível
     * @param {number} seconds - Duração em segundos
     * @returns {string} Duração formatada
     */
    formatDuration(seconds) {
        if (!seconds) return 'N/A';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
}

module.exports = YtDlpDownloader;
