# 🎬 Movie Stash

**Movie Stash** é um aplicativo desktop desenvolvido em Electron que permite catalogar e baixar filmes localmente. O app funciona 100% offline após a instalação, com integração à API do TMDb para busca de filmes e suporte a múltiplos métodos de download.

## ✨ Funcionalidades

### 🔍 Busca e Catálogo
- **Busca de filmes** via API do TMDb
- **Informações completas**: título, ano, sinopse, gênero, capa, elenco
- **Catálogo local** armazenado em JSON
- **Filtros** por status, gênero e busca por texto

### ⬇️ Downloads
- **HTTP/HTTPS**: Download direto de arquivos
- **Torrents**: Suporte a magnet links e arquivos .torrent
- **yt-dlp**: Download de vídeos do YouTube e outros sites
- **Monitoramento de progresso** em tempo real
- **Múltiplas qualidades** (1080p, 720p, 480p, etc.)

### 🎨 Interface
- **Design moderno** e intuitivo
- **Tema escuro** premium
- **Responsivo** para diferentes tamanhos de tela
- **Notificações** em tempo real
- **Modais** para detalhes e configurações

## 🚀 Instalação

### Pré-requisitos

1. **Node.js** (versão 16 ou superior)
   ```bash
   # Verificar versão
   node --version
   npm --version
   ```

2. **yt-dlp** (para download de vídeos)
   ```bash
   # Windows (via pip)
   pip install yt-dlp
   
   # Ou baixar executável em: https://github.com/yt-dlp/yt-dlp/releases
   ```

### Instalação do Projeto

1. **Clone ou baixe** este projeto
2. **Instale as dependências**:
   ```bash
   npm install
   ```

3. **Execute o aplicativo**:
   ```bash
   npm start
   ```

## 📁 Estrutura do Projeto

```
movie-stash/
├── 📁 backend/              # Lógica de backend (Node.js)
│   ├── httpDownloader.js    # Download via HTTP/HTTPS
│   ├── torrentDownloader.js # Download via torrents
│   ├── ytDlpDownloader.js   # Download via yt-dlp
│   ├── catalogManager.js    # Gerenciamento do catálogo
│   ├── tmdbApi.js          # Integração com TMDb API
│   └── index.js            # Classe principal MovieStash
├── 📁 frontend/            # Interface do usuário
│   ├── index.html          # Página principal
│   ├── styles.css          # Estilos CSS
│   ├── script.js           # JavaScript da interface
│   └── preload.js          # Bridge Electron (segurança)
├── 📁 database/            # Catálogo local (JSON)
├── 📁 downloads/           # Arquivos baixados
├── 📁 assets/              # Ícones e recursos
├── main.js                 # Processo principal do Electron
├── package.json            # Dependências e scripts
└── README.md              # Este arquivo
```

## 🎯 Como Usar

### 1. Primeira Execução
- Execute `npm start`
- O aplicativo abrirá automaticamente
- A API do TMDb já está configurada

### 2. Buscar Filmes
- Vá para a aba **"Buscar"**
- Digite o nome do filme
- Clique em **"Buscar"** ou pressione Enter
- Navegue pelos resultados

### 3. Adicionar ao Catálogo
- Clique em **"Detalhes"** para ver informações completas
- Clique em **"Adicionar"** para salvar no catálogo local
- O filme aparecerá na aba **"Catálogo"**

### 4. Fazer Download
- Na aba **"Catálogo"**, clique em **"Download"** no filme desejado
- Cole o link de download (HTTP, torrent ou vídeo)
- Escolha a qualidade desejada
- Clique em **"Iniciar Download"**

### 5. Tipos de Links Suportados

#### HTTP/HTTPS
```
https://exemplo.com/filme.mp4
https://servidor.com/download/filme.mkv
```

#### Torrents
```
magnet:?xt=urn:btih:...
https://site.com/filme.torrent
```

#### Vídeos (yt-dlp)
```
https://www.youtube.com/watch?v=...
https://vimeo.com/...
https://dailymotion.com/...
```

## ⚙️ Configurações

### Pasta de Downloads
- Vá para a aba **"Configurações"**
- Clique em **"Selecionar"** para escolher onde salvar os arquivos
- Por padrão, os arquivos são salvos em `./downloads/`

### API do TMDb
- A chave da API já está configurada
- Para usar sua própria chave, edite o arquivo `backend/tmdbApi.js`

## 🛠️ Desenvolvimento

### Scripts Disponíveis
```bash
# Executar em modo desenvolvimento
npm run dev

# Executar normalmente
npm start

# Testar backend isoladamente
npm test

# Build para distribuição
npm run build
```

### Estrutura do Backend

#### HttpDownloader
```javascript
const downloader = new HttpDownloader();
await downloader.downloadFile('https://exemplo.com/filme.mp4');
```

#### TorrentDownloader
```javascript
const downloader = new TorrentDownloader();
await downloader.downloadTorrent('magnet:?xt=urn:btih:...');
```

#### YtDlpDownloader
```javascript
const downloader = new YtDlpDownloader();
await downloader.downloadVideo('https://youtube.com/watch?v=...', {
    quality: '720p'
});
```

#### CatalogManager
```javascript
const catalog = new CatalogManager();
catalog.addMovie({
    title: 'Matrix',
    year: 1999,
    genre: ['Ação', 'Ficção Científica']
});
```

## 🔧 Personalização

### Modificar Tema
- Edite as variáveis CSS em `frontend/styles.css`
- Altere as cores em `:root { --primary-color: #2563eb; }`

### Adicionar Novos Downloaders
1. Crie um novo arquivo em `backend/`
2. Implemente a interface padrão
3. Integre no `backend/index.js`
4. Adicione suporte no frontend

### Modificar Layout
- Edite `frontend/index.html` para estrutura
- Modifique `frontend/styles.css` para estilos
- Atualize `frontend/script.js` para funcionalidade

## 🐛 Solução de Problemas

### yt-dlp não encontrado
```bash
# Instalar yt-dlp
pip install yt-dlp

# Ou adicionar ao PATH do sistema
```

### Erro de permissão na pasta de downloads
- Verifique se a pasta tem permissão de escrita
- Execute como administrador se necessário

### API do TMDb não funciona
- Verifique sua conexão com a internet
- A chave da API pode ter expirado

### Downloads não iniciam
- Verifique se o link está correto
- Teste o link em um navegador primeiro
- Verifique os logs no console (F12)

## 📝 Licença

Este projeto é licenciado sob a MIT License - veja o arquivo LICENSE para detalhes.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📞 Suporte

- **Issues**: Reporte bugs e solicite features
- **Documentação**: Consulte este README
- **Logs**: Verifique o console do Electron (F12)

---

**Movie Stash** - Seu catálogo pessoal de filmes! 🎬✨
