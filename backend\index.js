const HttpDownloader = require('./httpDownloader');
const TorrentDownloader = require('./torrentDownloader');
const YtDlpDownloader = require('./ytDlpDownloader');
const CatalogManager = require('./catalogManager');
const TmdbApi = require('./tmdbApi');

/**
 * Classe principal que integra todos os módulos do Movie Stash
 */
class MovieStash {
    constructor() {
        // Inicializa todos os módulos
        this.httpDownloader = new HttpDownloader();
        this.torrentDownloader = new TorrentDownloader();
        this.ytDlpDownloader = new YtDlpDownloader();
        this.catalogManager = new CatalogManager();
        
        // Inicializa API do TMDb com sua chave
        this.tmdbApi = new TmdbApi('545f2df151ee4d8c39eeb33245a600c0');
        
        console.log('🎬 Movie Stash inicializado com sucesso!');
    }

    /**
     * Busca filme no TMDb e adiciona ao catálogo
     * @param {string} movieTitle - Título do filme
     * @returns {Promise<Array>} Lista de filmes encontrados
     */
    async searchAndCatalogMovie(movieTitle) {
        try {
            console.log(`\n🔍 Buscando filme: "${movieTitle}"`);
            
            // Busca no TMDb
            const searchResults = await this.tmdbApi.searchMovies(movieTitle);
            
            if (searchResults.results.length === 0) {
                console.log(`❌ Nenhum filme encontrado para: "${movieTitle}"`);
                return [];
            }

            console.log(`\n📋 Filmes encontrados:`);
            searchResults.results.forEach((movie, index) => {
                console.log(`${index + 1}. ${movie.title} (${movie.year}) - Rating: ${movie.rating}/10`);
            });

            return searchResults.results;

        } catch (error) {
            console.error(`❌ Erro ao buscar filme: ${error.message}`);
            return [];
        }
    }

    /**
     * Adiciona filme ao catálogo com detalhes completos do TMDb
     * @param {number} tmdbId - ID do filme no TMDb
     * @returns {Promise<Object|null>} Filme adicionado ou null
     */
    async addMovieToLocalCatalog(tmdbId) {
        try {
            console.log(`\n📚 Adicionando filme ao catálogo local...`);
            
            // Obtém detalhes completos do filme
            const movieDetails = await this.tmdbApi.getMovieDetails(tmdbId);
            
            // Adiciona ao catálogo local
            const catalogedMovie = this.catalogManager.addMovie({
                title: movieDetails.title,
                year: movieDetails.year,
                genre: movieDetails.genre,
                synopsis: movieDetails.synopsis,
                poster: movieDetails.poster,
                tmdbId: movieDetails.tmdbId,
                imdbId: movieDetails.imdbId,
                rating: movieDetails.rating,
                runtime: movieDetails.runtime,
                director: movieDetails.director,
                cast: movieDetails.cast.map(actor => actor.name)
            });

            console.log(`✅ Filme adicionado ao catálogo local!`);
            return catalogedMovie;

        } catch (error) {
            console.error(`❌ Erro ao adicionar filme ao catálogo: ${error.message}`);
            return null;
        }
    }

    /**
     * Faz download via HTTP e associa ao filme no catálogo
     * @param {string} movieId - ID do filme no catálogo
     * @param {string} downloadUrl - URL para download
     * @param {string} customFileName - Nome personalizado do arquivo
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async downloadMovieHttp(movieId, downloadUrl, customFileName = null) {
        try {
            console.log(`\n⬇️  Iniciando download HTTP...`);
            
            // Atualiza status do filme para "downloading"
            this.catalogManager.updateMovie(movieId, { status: 'downloading' });
            
            // Faz o download
            const result = await this.httpDownloader.downloadFile(downloadUrl, customFileName);
            
            if (result.success) {
                // Adiciona arquivo baixado ao catálogo
                this.catalogManager.addDownloadedFile(movieId, {
                    fileName: result.fileName,
                    filePath: result.filePath,
                    size: result.size,
                    sizeFormatted: result.sizeFormatted,
                    downloadMethod: 'http'
                });
                
                console.log(`✅ Download HTTP concluído e adicionado ao catálogo!`);
                return true;
            }
            
            return false;

        } catch (error) {
            console.error(`❌ Erro no download HTTP: ${error.message}`);
            // Reverte status do filme
            this.catalogManager.updateMovie(movieId, { status: 'cataloged' });
            return false;
        }
    }

    /**
     * Faz download via torrent e associa ao filme no catálogo
     * @param {string} movieId - ID do filme no catálogo
     * @param {string} magnetOrTorrentUrl - Magnet link ou URL do torrent
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async downloadMovieTorrent(movieId, magnetOrTorrentUrl) {
        try {
            console.log(`\n🧲 Iniciando download via torrent...`);
            
            // Atualiza status do filme para "downloading"
            this.catalogManager.updateMovie(movieId, { status: 'downloading' });
            
            // Faz o download
            const result = await this.torrentDownloader.downloadTorrent(magnetOrTorrentUrl);
            
            if (result.success) {
                // Adiciona arquivos baixados ao catálogo
                result.files.forEach(file => {
                    this.catalogManager.addDownloadedFile(movieId, {
                        fileName: file.name,
                        filePath: file.path,
                        size: file.size,
                        sizeFormatted: file.sizeFormatted,
                        downloadMethod: 'torrent'
                    });
                });
                
                console.log(`✅ Download via torrent concluído e adicionado ao catálogo!`);
                return true;
            }
            
            return false;

        } catch (error) {
            console.error(`❌ Erro no download via torrent: ${error.message}`);
            // Reverte status do filme
            this.catalogManager.updateMovie(movieId, { status: 'cataloged' });
            return false;
        }
    }

    /**
     * Faz download via yt-dlp e associa ao filme no catálogo
     * @param {string} movieId - ID do filme no catálogo
     * @param {string} videoUrl - URL do vídeo
     * @param {Object} options - Opções de download
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async downloadMovieYtDlp(movieId, videoUrl, options = {}) {
        try {
            console.log(`\n🎬 Iniciando download via yt-dlp...`);
            
            // Atualiza status do filme para "downloading"
            this.catalogManager.updateMovie(movieId, { status: 'downloading' });
            
            // Faz o download
            const result = await this.ytDlpDownloader.downloadVideo(videoUrl, options);
            
            if (result.success && result.filePath) {
                // Adiciona arquivo baixado ao catálogo
                this.catalogManager.addDownloadedFile(movieId, {
                    fileName: result.fileName,
                    filePath: result.filePath,
                    size: result.size,
                    sizeFormatted: result.sizeFormatted,
                    quality: options.quality || 'best',
                    downloadMethod: 'ytdlp'
                });
                
                console.log(`✅ Download via yt-dlp concluído e adicionado ao catálogo!`);
                return true;
            }
            
            return false;

        } catch (error) {
            console.error(`❌ Erro no download via yt-dlp: ${error.message}`);
            // Reverte status do filme
            this.catalogManager.updateMovie(movieId, { status: 'cataloged' });
            return false;
        }
    }

    /**
     * Lista filmes do catálogo local
     * @param {Object} filters - Filtros opcionais
     */
    listLocalMovies(filters = {}) {
        console.log(`\n📚 Filmes no catálogo local:`);
        
        const movies = this.catalogManager.listMovies(filters);
        
        if (movies.length === 0) {
            console.log(`📭 Nenhum filme encontrado no catálogo.`);
            return;
        }

        movies.forEach((movie, index) => {
            const status = movie.status === 'downloaded' ? '✅' : 
                          movie.status === 'downloading' ? '⬇️' : '📋';
            
            console.log(`${index + 1}. ${status} ${movie.title} (${movie.year})`);
            console.log(`   Gênero: ${movie.genre.join(', ')}`);
            console.log(`   Status: ${movie.status}`);
            console.log(`   Arquivos baixados: ${movie.downloadedFiles.length}`);
            console.log('');
        });
    }

    /**
     * Mostra estatísticas do catálogo
     */
    showStats() {
        console.log(`\n📊 Estatísticas do Movie Stash:`);
        
        const stats = this.catalogManager.getStats();
        
        if (stats) {
            console.log(`📚 Total de filmes: ${stats.totalMovies}`);
            console.log(`✅ Filmes baixados: ${stats.downloadedMovies}`);
            console.log(`📋 Filmes catalogados: ${stats.catalogedMovies}`);
            console.log(`⬇️  Filmes baixando: ${stats.downloadingMovies}`);
            console.log(`💾 Tamanho do catálogo: ${stats.catalogSize}`);
            
            if (stats.topGenres.length > 0) {
                console.log(`\n🎭 Gêneros mais populares:`);
                stats.topGenres.forEach((genre, index) => {
                    console.log(`${index + 1}. ${genre.genre}: ${genre.count} filmes`);
                });
            }
        }
    }

    /**
     * Limpa recursos e finaliza aplicação
     */
    cleanup() {
        console.log(`\n🧹 Limpando recursos...`);
        this.torrentDownloader.destroy();
        console.log(`👋 Movie Stash finalizado!`);
    }
}

// Exemplo de uso
async function exemploDeUso() {
    const movieStash = new MovieStash();
    
    try {
        console.log('🎬 === EXEMPLO DE USO DO MOVIE STASH ===\n');
        
        // 1. Buscar filme no TMDb
        const searchResults = await movieStash.searchAndCatalogMovie('Matrix');
        
        if (searchResults.length > 0) {
            // 2. Adicionar primeiro resultado ao catálogo local
            const movie = await movieStash.addMovieToLocalCatalog(searchResults[0].tmdbId);
            
            if (movie) {
                console.log(`\n🎯 Filme selecionado: ${movie.title} (ID: ${movie.id})`);
                
                // 3. Adicionar link de download (exemplo)
                movieStash.catalogManager.addDownloadLink(movie.id, {
                    url: 'https://example.com/matrix.mp4',
                    type: 'http',
                    quality: '1080p',
                    notes: 'Link de exemplo'
                });
                
                // 4. Exemplo de download HTTP (descomente para testar com URL real)
                // await movieStash.downloadMovieHttp(movie.id, 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4');
                
                // 5. Exemplo de download yt-dlp (descomente para testar com URL real)
                // await movieStash.downloadMovieYtDlp(movie.id, 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', { quality: '720p' });
            }
        }
        
        // 6. Listar filmes do catálogo
        movieStash.listLocalMovies();
        
        // 7. Mostrar estatísticas
        movieStash.showStats();
        
    } catch (error) {
        console.error(`❌ Erro no exemplo: ${error.message}`);
    } finally {
        // Limpa recursos
        movieStash.cleanup();
    }
}

// Executa exemplo se o arquivo for executado diretamente
if (require.main === module) {
    exemploDeUso();
}

module.exports = MovieStash;
