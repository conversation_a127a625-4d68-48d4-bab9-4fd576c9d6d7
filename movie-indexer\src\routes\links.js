/**
 * <PERSON>otas de Links
 */

const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const MovieLink = require('../models/MovieLink');
const linkValidator = require('../services/linkValidator');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Middleware de validação
 */
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Dados inválidos',
            details: errors.array()
        });
    }
    next();
};

/**
 * GET /api/links
 * Lista links com filtros e paginação
 */
router.get('/', [
    query('page').optional().isInt({ min: 1 }).withMessage('Página deve ser um número positivo'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite deve ser entre 1 e 100'),
    query('resolution').optional().isIn(['480p', '720p', '1080p', '4k']).withMessage('Resolução inválida'),
    query('provider').optional().isLength({ min: 1, max: 50 }).withMessage('Provedor inválido'),
    query('verified').optional().isBoolean().withMessage('Verified deve ser boolean'),
    query('sortBy').optional().isIn(['created_at', 'resolution', 'provider', 'download_count', 'stream_count']).withMessage('Campo de ordenação inválido'),
    query('sortOrder').optional().isIn(['ASC', 'DESC']).withMessage('Ordem deve ser ASC ou DESC')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        resolution: req.query.resolution || '',
        provider: req.query.provider || '',
        verified: req.query.verified !== undefined ? req.query.verified === 'true' : null,
        sortBy: req.query.sortBy || 'created_at',
        sortOrder: req.query.sortOrder || 'DESC'
    };

    const result = await MovieLink.findAll(options);
    
    logger.activity('list', 'links', { filters: options }, req.ip);

    res.json({
        success: true,
        data: result.links,
        pagination: result.pagination
    });
}));

/**
 * GET /api/links/:id
 * Obtém detalhes de um link
 */
router.get('/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const link = await MovieLink.findById(req.params.id);
    
    if (!link) {
        return res.status(404).json({
            success: false,
            error: 'Link não encontrado'
        });
    }

    logger.activity('view', 'link', { linkId: link.id, provider: link.provider }, req.ip);

    res.json({
        success: true,
        data: link
    });
}));

/**
 * PUT /api/links/:id
 * Atualiza um link
 */
router.put('/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo'),
    body('url').optional().isURL().withMessage('URL inválida'),
    body('resolution').optional().isIn(['480p', '720p', '1080p', '4k']).withMessage('Resolução inválida'),
    body('quality').optional().isLength({ max: 50 }).withMessage('Qualidade inválida'),
    body('language').optional().isLength({ max: 10 }).withMessage('Idioma inválido'),
    body('user_notes').optional().isLength({ max: 500 }).withMessage('Notas devem ter até 500 caracteres')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const link = await MovieLink.findById(req.params.id);
    
    if (!link) {
        return res.status(404).json({
            success: false,
            error: 'Link não encontrado'
        });
    }

    // Atualizar propriedades
    Object.keys(req.body).forEach(key => {
        if (req.body[key] !== undefined) {
            link[key] = req.body[key];
        }
    });

    // Se URL foi alterada, detectar novo provedor e invalidar verificação
    if (req.body.url) {
        link.provider = linkValidator.detectProvider(req.body.url);
        link.is_verified = false;
        link.verification_status = 'pending';
        link.verification_error = '';
        link.last_verified = null;
    }

    await link.save();
    
    logger.activity('update', 'link', { linkId: link.id, provider: link.provider }, req.ip);

    res.json({
        success: true,
        data: link,
        message: 'Link atualizado com sucesso'
    });
}));

/**
 * DELETE /api/links/:id
 * Remove um link
 */
router.delete('/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const link = await MovieLink.findById(req.params.id);
    
    if (!link) {
        return res.status(404).json({
            success: false,
            error: 'Link não encontrado'
        });
    }

    await link.delete();
    
    logger.activity('delete', 'link', { linkId: link.id, provider: link.provider }, req.ip);

    res.json({
        success: true,
        message: 'Link removido com sucesso'
    });
}));

/**
 * POST /api/links/:id/validate
 * Valida um link específico
 */
router.post('/:id/validate', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const link = await MovieLink.findById(req.params.id);
    
    if (!link) {
        return res.status(404).json({
            success: false,
            error: 'Link não encontrado'
        });
    }

    const result = await linkValidator.validateLink(link.url, link.provider);
    await link.updateVerificationStatus(result.isValid, result.error || '');
    
    logger.activity('validate', 'link', { 
        linkId: link.id, 
        provider: link.provider, 
        isValid: result.isValid 
    }, req.ip);

    res.json({
        success: true,
        data: {
            link: link,
            validation: result
        },
        message: `Link ${result.isValid ? 'válido' : 'inválido'}`
    });
}));

/**
 * POST /api/links/validate-batch
 * Valida múltiplos links em lote
 */
router.post('/validate-batch', [
    body('linkIds').isArray({ min: 1, max: 50 }).withMessage('Deve fornecer entre 1 e 50 IDs de links'),
    body('linkIds.*').isInt({ min: 1 }).withMessage('Todos os IDs devem ser números positivos')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const linkIds = req.body.linkIds;
    const links = [];
    
    // Buscar todos os links
    for (const id of linkIds) {
        const link = await MovieLink.findById(id);
        if (link) {
            links.push(link);
        }
    }

    if (links.length === 0) {
        return res.status(404).json({
            success: false,
            error: 'Nenhum link encontrado'
        });
    }

    // Validar em lote
    const results = await linkValidator.validateBatch(links);
    
    // Atualizar status de verificação
    for (const result of results) {
        const link = links.find(l => l.id === result.id);
        if (link) {
            await link.updateVerificationStatus(result.isValid, result.error || '');
        }
    }
    
    logger.activity('validate_batch', 'links', { 
        count: links.length, 
        valid: results.filter(r => r.isValid).length 
    }, req.ip);

    res.json({
        success: true,
        data: results,
        message: `${results.length} links validados`
    });
}));

/**
 * POST /api/links/:id/stream
 * Incrementa contador de stream e retorna URL
 */
router.post('/:id/stream', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const link = await MovieLink.findById(req.params.id);
    
    if (!link) {
        return res.status(404).json({
            success: false,
            error: 'Link não encontrado'
        });
    }

    if (!link.is_verified) {
        return res.status(400).json({
            success: false,
            error: 'Link não verificado'
        });
    }

    await link.incrementStreamCount();
    
    logger.activity('stream', 'link', { 
        linkId: link.id, 
        provider: link.provider,
        count: link.stream_count 
    }, req.ip);

    res.json({
        success: true,
        data: {
            url: link.url,
            provider: link.provider,
            resolution: link.resolution,
            stream_count: link.stream_count
        },
        message: 'Link de streaming obtido'
    });
}));

/**
 * POST /api/links/:id/download
 * Incrementa contador de download e retorna URL
 */
router.post('/:id/download', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const link = await MovieLink.findById(req.params.id);
    
    if (!link) {
        return res.status(404).json({
            success: false,
            error: 'Link não encontrado'
        });
    }

    if (!link.is_verified) {
        return res.status(400).json({
            success: false,
            error: 'Link não verificado'
        });
    }

    await link.incrementDownloadCount();
    
    logger.activity('download', 'link', { 
        linkId: link.id, 
        provider: link.provider,
        count: link.download_count 
    }, req.ip);

    res.json({
        success: true,
        data: {
            url: link.url,
            provider: link.provider,
            resolution: link.resolution,
            download_count: link.download_count
        },
        message: 'Link de download obtido'
    });
}));

/**
 * GET /api/links/stats/resolution
 * Obtém estatísticas por resolução
 */
router.get('/stats/resolution', asyncHandler(async (req, res) => {
    const stats = await MovieLink.getResolutionStats();
    
    res.json({
        success: true,
        data: stats
    });
}));

/**
 * GET /api/links/stats/provider
 * Obtém estatísticas por provedor
 */
router.get('/stats/provider', asyncHandler(async (req, res) => {
    const stats = await MovieLink.getProviderStats();
    
    res.json({
        success: true,
        data: stats
    });
}));

module.exports = router;
