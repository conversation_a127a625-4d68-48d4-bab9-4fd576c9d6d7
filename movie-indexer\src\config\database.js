/**
 * Configuração do Banco de Dados SQLite
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = process.env.DB_PATH || './data/movies.db';
    }

    /**
     * Inicializa o banco de dados
     */
    async initializeDatabase() {
        try {
            // Garantir que o diretório existe
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
                logger.info(`📁 Diretório criado: ${dbDir}`);
            }

            // Conectar ao banco
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    logger.error('❌ Erro ao conectar com SQLite:', err);
                    throw err;
                }
                logger.info(`🗄️ Conectado ao SQLite: ${this.dbPath}`);
            });

            // Habilitar foreign keys
            await this.run('PRAGMA foreign_keys = ON');
            
            // Criar tabelas
            await this.createTables();
            
            logger.info('✅ Banco de dados inicializado com sucesso');
            
        } catch (error) {
            logger.error('❌ Erro ao inicializar banco:', error);
            throw error;
        }
    }

    /**
     * Cria as tabelas do banco
     */
    async createTables() {
        const tables = [
            // Tabela de filmes
            `CREATE TABLE IF NOT EXISTS movies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tmdb_id INTEGER UNIQUE,
                title TEXT NOT NULL,
                original_title TEXT,
                year INTEGER,
                release_date TEXT,
                overview TEXT,
                poster_path TEXT,
                backdrop_path TEXT,
                genres TEXT, -- JSON array
                runtime INTEGER,
                vote_average REAL,
                vote_count INTEGER,
                popularity REAL,
                adult BOOLEAN DEFAULT 0,
                original_language TEXT,
                director TEXT,
                cast TEXT, -- JSON array
                production_companies TEXT, -- JSON array
                production_countries TEXT, -- JSON array
                spoken_languages TEXT, -- JSON array
                keywords TEXT, -- JSON array
                status TEXT DEFAULT 'active', -- active, archived, deleted
                user_rating INTEGER, -- 1-10
                user_notes TEXT,
                watched BOOLEAN DEFAULT 0,
                favorite BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de links de filmes
            `CREATE TABLE IF NOT EXISTS movie_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                movie_id INTEGER NOT NULL,
                url TEXT NOT NULL,
                provider TEXT NOT NULL, -- http, gdrive, mega, archive, etc
                resolution TEXT NOT NULL, -- 480p, 720p, 1080p, 4k
                quality TEXT, -- cam, ts, dvdrip, bluray, etc
                size_mb INTEGER,
                language TEXT DEFAULT 'pt-BR',
                subtitle_language TEXT,
                audio_format TEXT, -- mp3, aac, dts, etc
                video_format TEXT, -- mp4, mkv, avi, etc
                is_public BOOLEAN DEFAULT 1,
                is_verified BOOLEAN DEFAULT 0,
                last_verified DATETIME,
                verification_status TEXT, -- pending, valid, invalid, error
                verification_error TEXT,
                download_count INTEGER DEFAULT 0,
                stream_count INTEGER DEFAULT 0,
                user_notes TEXT,
                status TEXT DEFAULT 'active', -- active, inactive, broken
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (movie_id) REFERENCES movies (id) ON DELETE CASCADE
            )`,

            // Tabela de categorias/coleções
            `CREATE TABLE IF NOT EXISTS collections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                poster_path TEXT,
                movie_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de relacionamento filme-coleção
            `CREATE TABLE IF NOT EXISTS movie_collections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                movie_id INTEGER NOT NULL,
                collection_id INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (movie_id) REFERENCES movies (id) ON DELETE CASCADE,
                FOREIGN KEY (collection_id) REFERENCES collections (id) ON DELETE CASCADE,
                UNIQUE(movie_id, collection_id)
            )`,

            // Tabela de histórico de streaming
            `CREATE TABLE IF NOT EXISTS streaming_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                movie_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                quality TEXT NOT NULL,
                provider TEXT NOT NULL,
                magnet_link TEXT NOT NULL,
                duration_seconds INTEGER DEFAULT 0,
                progress_percent REAL DEFAULT 0,
                completed BOOLEAN DEFAULT FALSE,
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_watched_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                ended_at DATETIME,
                user_rating INTEGER,
                user_notes TEXT DEFAULT '',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (movie_id) REFERENCES movies (id) ON DELETE CASCADE
            )`,

            // Tabela de cache de qualidade de vídeo
            `CREATE TABLE IF NOT EXISTS video_quality_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                magnet_hash TEXT UNIQUE NOT NULL,
                detected_quality TEXT NOT NULL,
                resolution TEXT NOT NULL,
                codec TEXT,
                bitrate INTEGER,
                file_size INTEGER,
                confidence_score REAL DEFAULT 0,
                detection_method TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de logs de atividade
            `CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL, -- view, download, stream, add, update, delete
                resource_type TEXT NOT NULL, -- movie, link, collection
                resource_id INTEGER,
                details TEXT, -- JSON com detalhes da ação
                ip_address TEXT,
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Tabela de configurações
            `CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.run(table);
        }

        // Criar índices para performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_movies_tmdb_id ON movies(tmdb_id)',
            'CREATE INDEX IF NOT EXISTS idx_movies_title ON movies(title)',
            'CREATE INDEX IF NOT EXISTS idx_movies_year ON movies(year)',
            'CREATE INDEX IF NOT EXISTS idx_movies_status ON movies(status)',
            'CREATE INDEX IF NOT EXISTS idx_movies_created_at ON movies(created_at)',
            'CREATE INDEX IF NOT EXISTS idx_movie_links_movie_id ON movie_links(movie_id)',
            'CREATE INDEX IF NOT EXISTS idx_movie_links_resolution ON movie_links(resolution)',
            'CREATE INDEX IF NOT EXISTS idx_movie_links_provider ON movie_links(provider)',
            'CREATE INDEX IF NOT EXISTS idx_movie_links_status ON movie_links(status)',
            'CREATE INDEX IF NOT EXISTS idx_streaming_history_movie_id ON streaming_history(movie_id)',
            'CREATE INDEX IF NOT EXISTS idx_streaming_history_started_at ON streaming_history(started_at)',
            'CREATE INDEX IF NOT EXISTS idx_video_quality_cache_magnet_hash ON video_quality_cache(magnet_hash)',
            'CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON activity_logs(action)',
            'CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at)'
        ];

        for (const index of indexes) {
            await this.run(index);
        }

        // Inserir configurações padrão
        await this.insertDefaultSettings();
    }

    /**
     * Insere configurações padrão
     */
    async insertDefaultSettings() {
        const defaultSettings = [
            ['app_version', '1.0.0', 'Versão do aplicativo'],
            ['default_resolution', '1080p', 'Resolução padrão para novos links'],
            ['auto_verify_links', 'true', 'Verificar links automaticamente'],
            ['tmdb_language', 'pt-BR', 'Idioma padrão para busca no TMDb'],
            ['max_links_per_movie', '10', 'Máximo de links por filme'],
            ['cache_tmdb_results', 'true', 'Cache dos resultados do TMDb'],
            ['log_user_activity', 'true', 'Registrar atividade do usuário']
        ];

        for (const [key, value, description] of defaultSettings) {
            await this.run(
                'INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)',
                [key, value, description]
            );
        }
    }

    /**
     * Executa uma query SQL
     */
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    logger.error('❌ Erro SQL:', err);
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    /**
     * Executa uma query e retorna uma linha
     */
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    logger.error('❌ Erro SQL:', err);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * Executa uma query e retorna todas as linhas
     */
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    logger.error('❌ Erro SQL:', err);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * Fecha a conexão com o banco
     */
    close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        logger.error('❌ Erro ao fechar banco:', err);
                        reject(err);
                    } else {
                        logger.info('🔒 Conexão com banco fechada');
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * Obtém estatísticas do banco
     */
    async getStats() {
        try {
            const [movies, links, collections] = await Promise.all([
                this.get('SELECT COUNT(*) as count FROM movies WHERE status = "active"'),
                this.get('SELECT COUNT(*) as count FROM movie_links WHERE status = "active"'),
                this.get('SELECT COUNT(*) as count FROM collections')
            ]);

            return {
                movies: movies.count,
                links: links.count,
                collections: collections.count
            };
        } catch (error) {
            logger.error('❌ Erro ao obter estatísticas:', error);
            return { movies: 0, links: 0, collections: 0 };
        }
    }
}

// Instância singleton
const database = new Database();

module.exports = database;
