/**
 * Teste direto do addon manager
 */

const addonManager = require('./src/services/addonManager');

async function testAddonManager() {
    try {
        console.log('Testando addon manager...');
        
        // Verificar addons disponíveis
        console.log('1. Addons disponíveis:', addonManager.getAvailableAddons());
        
        // Dados de teste do filme
        const movieData = {
            id: 6,
            title: 'Clube da Luta',
            year: 1999,
            imdb_id: 'tt0137523',
            tmdb_id: 550
        };
        
        console.log('2. Dados do filme:', movieData);
        
        // Testar busca direta
        console.log('\n3. Testando busca direta...');
        const results1 = await addonManager.searchTorrents('Fight Club', {
            imdbId: 'tt0137523',
            tmdbId: 550,
            year: 1999
        });
        
        console.log('Resultados busca direta:', results1.length);
        if (results1.length > 0) {
            console.log('Primeiro resultado:', results1[0]);
        }
        
        // Testar busca por filme
        console.log('\n4. Testando busca por filme...');
        const results2 = await addonManager.searchMovie(movieData);
        
        console.log('Resultados busca por filme:', results2.length);
        if (results2.length > 0) {
            console.log('Primeiro resultado:', results2[0]);
        }
        
    } catch (error) {
        console.error('Erro no teste:', error.message);
        console.error('Stack:', error.stack);
    }
}

testAddonManager();
