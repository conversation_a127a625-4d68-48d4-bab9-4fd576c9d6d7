const WebTorrent = require('webtorrent');
const path = require('path');
const fs = require('fs');

/**
 * Classe para fazer download de arquivos via torrent
 */
class TorrentDownloader {
    constructor() {
        this.client = new WebTorrent();
        this.downloadsPath = path.join(__dirname, '..', 'downloads');
        this.activeTorrents = new Map();
        this.ensureDownloadsDirectory();
        
        // Event listeners para o cliente
        this.setupClientEvents();
    }

    /**
     * Garante que a pasta de downloads existe
     */
    ensureDownloadsDirectory() {
        if (!fs.existsSync(this.downloadsPath)) {
            fs.mkdirSync(this.downloadsPath, { recursive: true });
            console.log(`📁 Pasta de downloads criada: ${this.downloadsPath}`);
        }
    }

    /**
     * Configura eventos do cliente WebTorrent
     */
    setupClientEvents() {
        this.client.on('error', (err) => {
            console.error('❌ Erro no cliente WebTorrent:', err.message);
        });

        this.client.on('torrent', (torrent) => {
            console.log(`🔗 Torrent adicionado: ${torrent.name || torrent.infoHash}`);
        });
    }

    /**
     * Converte bytes para formato legível
     * @param {number} bytes - Número de bytes
     * @returns {string} Tamanho formatado
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Formata velocidade de download
     * @param {number} bytesPerSecond - Bytes por segundo
     * @returns {string} Velocidade formatada
     */
    formatSpeed(bytesPerSecond) {
        return this.formatBytes(bytesPerSecond) + '/s';
    }

    /**
     * Formata tempo restante
     * @param {number} seconds - Segundos restantes
     * @returns {string} Tempo formatado
     */
    formatTime(seconds) {
        if (seconds === Infinity || isNaN(seconds)) return '∞';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    /**
     * Faz download de um torrent
     * @param {string} magnetOrTorrentUrl - Magnet link ou URL do arquivo .torrent
     * @param {Object} options - Opções de download
     * @returns {Promise<Object>} Resultado do download
     */
    async downloadTorrent(magnetOrTorrentUrl, options = {}) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`🧲 Iniciando download do torrent: ${magnetOrTorrentUrl}`);

                const torrentOptions = {
                    path: this.downloadsPath,
                    ...options
                };

                const torrent = this.client.add(magnetOrTorrentUrl, torrentOptions);
                
                // Armazena referência do torrent ativo
                this.activeTorrents.set(torrent.infoHash, torrent);

                // Timeout para metadados
                const metadataTimeout = setTimeout(() => {
                    console.error('❌ Timeout ao obter metadados do torrent');
                    this.client.remove(torrent);
                    this.activeTorrents.delete(torrent.infoHash);
                    reject(new Error('Timeout ao obter metadados do torrent'));
                }, 30000); // 30 segundos

                torrent.on('metadata', () => {
                    clearTimeout(metadataTimeout);
                    console.log(`📊 Metadados obtidos:`);
                    console.log(`   Nome: ${torrent.name}`);
                    console.log(`   Tamanho: ${this.formatBytes(torrent.length)}`);
                    console.log(`   Arquivos: ${torrent.files.length}`);
                    console.log(`   Peers: ${torrent.numPeers}`);

                    // Lista arquivos do torrent
                    console.log(`📁 Arquivos no torrent:`);
                    torrent.files.forEach((file, index) => {
                        console.log(`   ${index + 1}. ${file.name} (${this.formatBytes(file.length)})`);
                    });
                });

                // Monitor de progresso
                const progressInterval = setInterval(() => {
                    if (torrent.progress === 1) {
                        clearInterval(progressInterval);
                        return;
                    }

                    const progress = (torrent.progress * 100).toFixed(1);
                    const downloaded = this.formatBytes(torrent.downloaded);
                    const total = this.formatBytes(torrent.length);
                    const speed = this.formatSpeed(torrent.downloadSpeed);
                    const timeRemaining = this.formatTime(torrent.timeRemaining / 1000);
                    const peers = torrent.numPeers;

                    process.stdout.write(
                        `\r🔄 Progresso: ${progress}% | ` +
                        `${downloaded}/${total} | ` +
                        `⚡ ${speed} | ` +
                        `⏱️  ${timeRemaining} | ` +
                        `👥 ${peers} peers`
                    );
                }, 1000);

                torrent.on('done', () => {
                    clearInterval(progressInterval);
                    console.log(`\n✅ Download concluído!`);
                    console.log(`📁 Arquivos salvos em: ${this.downloadsPath}`);
                    console.log(`📊 Total baixado: ${this.formatBytes(torrent.length)}`);

                    // Lista arquivos baixados
                    const downloadedFiles = torrent.files.map(file => ({
                        name: file.name,
                        path: file.path,
                        size: file.length,
                        sizeFormatted: this.formatBytes(file.length)
                    }));

                    this.activeTorrents.delete(torrent.infoHash);

                    resolve({
                        success: true,
                        torrentName: torrent.name,
                        infoHash: torrent.infoHash,
                        totalSize: torrent.length,
                        totalSizeFormatted: this.formatBytes(torrent.length),
                        files: downloadedFiles,
                        downloadPath: this.downloadsPath
                    });
                });

                torrent.on('error', (err) => {
                    clearInterval(progressInterval);
                    console.error(`\n❌ Erro no download do torrent: ${err.message}`);
                    this.activeTorrents.delete(torrent.infoHash);
                    reject(err);
                });

                // Evento para quando não conseguir conectar com peers
                setTimeout(() => {
                    if (torrent.numPeers === 0 && torrent.progress === 0) {
                        console.warn('⚠️  Nenhum peer encontrado. Verifique se o torrent está ativo.');
                    }
                }, 10000); // 10 segundos

            } catch (error) {
                console.error(`❌ Erro ao iniciar download do torrent: ${error.message}`);
                reject(error);
            }
        });
    }

    /**
     * Pausa um torrent ativo
     * @param {string} infoHash - Hash do torrent
     */
    pauseTorrent(infoHash) {
        const torrent = this.activeTorrents.get(infoHash);
        if (torrent) {
            torrent.pause();
            console.log(`⏸️  Torrent pausado: ${torrent.name || infoHash}`);
        } else {
            console.log(`❌ Torrent não encontrado: ${infoHash}`);
        }
    }

    /**
     * Resume um torrent pausado
     * @param {string} infoHash - Hash do torrent
     */
    resumeTorrent(infoHash) {
        const torrent = this.activeTorrents.get(infoHash);
        if (torrent) {
            torrent.resume();
            console.log(`▶️  Torrent resumido: ${torrent.name || infoHash}`);
        } else {
            console.log(`❌ Torrent não encontrado: ${infoHash}`);
        }
    }

    /**
     * Remove um torrent
     * @param {string} infoHash - Hash do torrent
     * @param {boolean} deleteFiles - Se deve deletar os arquivos baixados
     */
    removeTorrent(infoHash, deleteFiles = false) {
        const torrent = this.activeTorrents.get(infoHash);
        if (torrent) {
            this.client.remove(torrent, { destroyStore: deleteFiles });
            this.activeTorrents.delete(infoHash);
            console.log(`🗑️  Torrent removido: ${torrent.name || infoHash}`);
            if (deleteFiles) {
                console.log(`🗑️  Arquivos deletados`);
            }
        } else {
            console.log(`❌ Torrent não encontrado: ${infoHash}`);
        }
    }

    /**
     * Lista torrents ativos
     * @returns {Array} Lista de torrents ativos
     */
    getActiveTorrents() {
        const torrents = [];
        this.activeTorrents.forEach((torrent, infoHash) => {
            torrents.push({
                infoHash: infoHash,
                name: torrent.name,
                progress: torrent.progress,
                progressPercent: (torrent.progress * 100).toFixed(1),
                downloaded: torrent.downloaded,
                downloadedFormatted: this.formatBytes(torrent.downloaded),
                length: torrent.length,
                lengthFormatted: this.formatBytes(torrent.length),
                downloadSpeed: torrent.downloadSpeed,
                downloadSpeedFormatted: this.formatSpeed(torrent.downloadSpeed),
                numPeers: torrent.numPeers,
                timeRemaining: torrent.timeRemaining,
                timeRemainingFormatted: this.formatTime(torrent.timeRemaining / 1000)
            });
        });
        return torrents;
    }

    /**
     * Destrói o cliente WebTorrent
     */
    destroy() {
        this.client.destroy((err) => {
            if (err) {
                console.error('❌ Erro ao destruir cliente WebTorrent:', err.message);
            } else {
                console.log('🔌 Cliente WebTorrent desconectado');
            }
        });
    }
}

module.exports = TorrentDownloader;
