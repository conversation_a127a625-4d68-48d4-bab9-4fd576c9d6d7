/* ===== RESET E VARIÁVEIS ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Cores principais */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    
    /* Cores de fundo */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-modal: rgba(15, 23, 42, 0.95);
    
    /* Cores de texto */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* Cores de status */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* <PERSON><PERSON><PERSON> e sombras */
    --border-color: #334155;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* Transições */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Espaçamentos */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* ===== BASE STYLES ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* ===== HEADER ===== */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--primary-color);
}

.logo i {
    font-size: 1.5rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== NAVIGATION TABS ===== */
.nav-tabs {
    display: flex;
    gap: var(--spacing-xs);
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-tab:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--primary-color);
    color: white;
}

.nav-tab i {
    font-size: 0.9rem;
}

/* ===== HEADER ACTIONS ===== */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border: none;
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
}

.stats-display {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

#totalMovies {
    color: var(--primary-color);
    font-weight: 600;
}

/* ===== MAIN CONTENT ===== */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    min-height: calc(100vh - 80px);
}

/* ===== TAB CONTENT ===== */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== BUTTONS ===== */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--secondary-color);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-error {
    background: var(--error-color);
    color: white;
}

/* ===== SEARCH SECTION ===== */
.search-section {
    max-width: 800px;
    margin: 0 auto;
}

.search-bar {
    margin-bottom: var(--spacing-2xl);
}

.search-input-group {
    display: flex;
    gap: var(--spacing-sm);
    background: var(--bg-secondary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

#searchInput {
    flex: 1;
    padding: var(--spacing-md);
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 1rem;
    outline: none;
}

#searchInput::placeholder {
    color: var(--text-muted);
}

/* ===== WELCOME MESSAGE ===== */
.welcome-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.welcome-message i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.welcome-message h2 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

/* ===== MOVIE CARDS ===== */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.movie-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.movie-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.movie-poster {
    position: relative;
    aspect-ratio: 2/3;
    overflow: hidden;
}

.movie-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.movie-card:hover .movie-poster img {
    transform: scale(1.05);
}

.movie-rating {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: rgba(0, 0, 0, 0.8);
    color: var(--accent-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
}

.movie-info {
    padding: var(--spacing-md);
}

.movie-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    line-height: 1.3;
}

.movie-year {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
}

.movie-genres {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.genre-tag {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.movie-synopsis {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.movie-actions {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-sm);
}

/* ===== CATALOG SECTION ===== */
.catalog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.catalog-header h2 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
}

.catalog-filters {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.catalog-filters select,
.catalog-filters input {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.catalog-filters select:focus,
.catalog-filters input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.catalog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.empty-catalog,
.empty-downloads {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-catalog i,
.empty-downloads i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
}

.empty-catalog h3,
.empty-downloads h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

/* ===== STATUS INDICATORS ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-cataloged {
    background: rgba(100, 116, 139, 0.2);
    color: var(--secondary-color);
}

.status-downloading {
    background: rgba(59, 130, 246, 0.2);
    color: var(--info-color);
}

.status-downloaded {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

/* ===== DOWNLOADS SECTION ===== */
.downloads-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.downloads-header h2 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
}

.downloads-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.download-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
}

.download-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.download-title {
    font-weight: 600;
    color: var(--text-primary);
}

.download-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.download-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width var(--transition-normal);
}

.download-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* ===== SETTINGS SECTION ===== */
.settings-section h2 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.settings-group {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.settings-group h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 1.1rem;
}

.setting-item {
    margin-bottom: var(--spacing-md);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.folder-selector {
    display: flex;
    gap: var(--spacing-sm);
}

.folder-selector input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: var(--spacing-xs);
}

.about-info {
    color: var(--text-secondary);
    line-height: 1.6;
}

.about-info p {
    margin-bottom: var(--spacing-sm);
}

.about-info strong {
    color: var(--text-primary);
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-20px) scale(0.95); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

/* ===== MOVIE DETAILS MODAL ===== */
.movie-details {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-xl);
    max-width: 900px;
}

.movie-details-poster {
    aspect-ratio: 2/3;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.movie-details-poster img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.movie-details-info h1 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.8rem;
}

.movie-details-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.movie-details-synopsis {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.movie-details-cast {
    margin-bottom: var(--spacing-lg);
}

.movie-details-cast h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.cast-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.cast-member {
    background: var(--bg-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.movie-details-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* ===== FORM STYLES ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.8);
    z-index: 2000;
    backdrop-filter: blur(3px);
}

.loading-overlay.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.loading-spinner p {
    color: var(--text-secondary);
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    min-width: 300px;
    box-shadow: var(--shadow-lg);
    animation: toastSlideIn 0.3s ease;
}

@keyframes toastSlideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
}

.toast-close:hover {
    background: var(--bg-tertiary);
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .nav-tabs {
        order: 2;
        width: 100%;
        justify-content: center;
    }

    .header-actions {
        order: 1;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .movies-grid,
    .catalog-grid {
        grid-template-columns: 1fr;
    }

    .movie-details {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .catalog-header {
        flex-direction: column;
        align-items: stretch;
    }

    .catalog-filters {
        justify-content: stretch;
    }

    .catalog-filters select,
    .catalog-filters input {
        flex: 1;
    }

    .modal-content {
        margin: var(--spacing-md);
        max-width: calc(100vw - 2rem);
    }

    .form-actions {
        flex-direction: column;
    }

    .toast {
        min-width: auto;
        margin: 0 var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        flex: 1;
        justify-content: center;
        min-width: calc(50% - var(--spacing-xs));
    }

    .search-input-group {
        flex-direction: column;
    }

    .movie-actions {
        flex-direction: column;
    }

    .downloads-header {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}
