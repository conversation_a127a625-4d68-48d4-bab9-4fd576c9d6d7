const fs = require('fs');
const path = require('path');

/**
 * Classe para gerenciar o catálogo de filmes
 */
class CatalogManager {
    constructor() {
        this.databasePath = path.join(__dirname, '..', 'database');
        this.catalogFile = path.join(this.databasePath, 'movies_catalog.json');
        this.ensureDatabaseDirectory();
        this.loadCatalog();
    }

    /**
     * Garante que a pasta de database existe
     */
    ensureDatabaseDirectory() {
        if (!fs.existsSync(this.databasePath)) {
            fs.mkdirSync(this.databasePath, { recursive: true });
            console.log(`📁 Pasta de database criada: ${this.databasePath}`);
        }
    }

    /**
     * Carrega o catálogo do arquivo JSON
     */
    loadCatalog() {
        try {
            if (fs.existsSync(this.catalogFile)) {
                const data = fs.readFileSync(this.catalogFile, 'utf8');
                this.catalog = JSON.parse(data);
                console.log(`📚 Catálogo carregado: ${this.catalog.movies.length} filmes`);
            } else {
                this.catalog = {
                    version: '1.0.0',
                    created: new Date().toISOString(),
                    lastModified: new Date().toISOString(),
                    movies: []
                };
                this.saveCatalog();
                console.log(`📚 Novo catálogo criado`);
            }
        } catch (error) {
            console.error(`❌ Erro ao carregar catálogo: ${error.message}`);
            this.catalog = {
                version: '1.0.0',
                created: new Date().toISOString(),
                lastModified: new Date().toISOString(),
                movies: []
            };
        }
    }

    /**
     * Salva o catálogo no arquivo JSON
     */
    saveCatalog() {
        try {
            this.catalog.lastModified = new Date().toISOString();
            const data = JSON.stringify(this.catalog, null, 2);
            fs.writeFileSync(this.catalogFile, data, 'utf8');
            console.log(`💾 Catálogo salvo: ${this.catalog.movies.length} filmes`);
        } catch (error) {
            console.error(`❌ Erro ao salvar catálogo: ${error.message}`);
            throw error;
        }
    }

    /**
     * Gera um ID único para o filme
     * @returns {string} ID único
     */
    generateMovieId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Adiciona um filme ao catálogo
     * @param {Object} movieData - Dados do filme
     * @returns {Object} Filme adicionado
     */
    addMovie(movieData) {
        try {
            // Validação básica
            if (!movieData.title) {
                throw new Error('Título do filme é obrigatório');
            }

            // Verifica se o filme já existe
            const existingMovie = this.catalog.movies.find(
                movie => movie.title.toLowerCase() === movieData.title.toLowerCase() &&
                         movie.year === movieData.year
            );

            if (existingMovie) {
                console.log(`⚠️  Filme já existe no catálogo: ${movieData.title} (${movieData.year})`);
                return existingMovie;
            }

            // Cria objeto do filme
            const movie = {
                id: this.generateMovieId(),
                title: movieData.title,
                year: movieData.year || null,
                genre: movieData.genre || [],
                synopsis: movieData.synopsis || '',
                poster: movieData.poster || '',
                tmdbId: movieData.tmdbId || null,
                imdbId: movieData.imdbId || null,
                rating: movieData.rating || null,
                runtime: movieData.runtime || null,
                director: movieData.director || '',
                cast: movieData.cast || [],
                downloadLinks: movieData.downloadLinks || [],
                downloadedFiles: movieData.downloadedFiles || [],
                quality: movieData.quality || '',
                status: movieData.status || 'cataloged', // cataloged, downloading, downloaded, watched
                dateAdded: new Date().toISOString(),
                lastModified: new Date().toISOString(),
                notes: movieData.notes || ''
            };

            // Adiciona ao catálogo
            this.catalog.movies.push(movie);
            this.saveCatalog();

            console.log(`✅ Filme adicionado ao catálogo: ${movie.title} (${movie.year})`);
            return movie;

        } catch (error) {
            console.error(`❌ Erro ao adicionar filme: ${error.message}`);
            throw error;
        }
    }

    /**
     * Lista todos os filmes do catálogo
     * @param {Object} filters - Filtros opcionais
     * @returns {Array} Lista de filmes
     */
    listMovies(filters = {}) {
        try {
            let movies = [...this.catalog.movies];

            // Aplica filtros
            if (filters.genre) {
                movies = movies.filter(movie => 
                    movie.genre.some(g => 
                        g.toLowerCase().includes(filters.genre.toLowerCase())
                    )
                );
            }

            if (filters.year) {
                movies = movies.filter(movie => movie.year === filters.year);
            }

            if (filters.status) {
                movies = movies.filter(movie => movie.status === filters.status);
            }

            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                movies = movies.filter(movie => 
                    movie.title.toLowerCase().includes(searchTerm) ||
                    movie.synopsis.toLowerCase().includes(searchTerm) ||
                    movie.director.toLowerCase().includes(searchTerm) ||
                    movie.cast.some(actor => actor.toLowerCase().includes(searchTerm))
                );
            }

            // Ordena por data de adição (mais recente primeiro)
            movies.sort((a, b) => new Date(b.dateAdded) - new Date(a.dateAdded));

            return movies;

        } catch (error) {
            console.error(`❌ Erro ao listar filmes: ${error.message}`);
            return [];
        }
    }

    /**
     * Busca filme por ID
     * @param {string} movieId - ID do filme
     * @returns {Object|null} Filme encontrado ou null
     */
    getMovieById(movieId) {
        try {
            const movie = this.catalog.movies.find(movie => movie.id === movieId);
            return movie || null;
        } catch (error) {
            console.error(`❌ Erro ao buscar filme por ID: ${error.message}`);
            return null;
        }
    }

    /**
     * Busca filmes por nome
     * @param {string} title - Título do filme
     * @returns {Array} Lista de filmes encontrados
     */
    searchMoviesByTitle(title) {
        try {
            const searchTerm = title.toLowerCase();
            const movies = this.catalog.movies.filter(movie => 
                movie.title.toLowerCase().includes(searchTerm)
            );
            
            console.log(`🔍 Encontrados ${movies.length} filmes para "${title}"`);
            return movies;

        } catch (error) {
            console.error(`❌ Erro ao buscar filmes: ${error.message}`);
            return [];
        }
    }

    /**
     * Atualiza um filme no catálogo
     * @param {string} movieId - ID do filme
     * @param {Object} updateData - Dados para atualizar
     * @returns {Object|null} Filme atualizado ou null
     */
    updateMovie(movieId, updateData) {
        try {
            const movieIndex = this.catalog.movies.findIndex(movie => movie.id === movieId);
            
            if (movieIndex === -1) {
                console.log(`❌ Filme não encontrado: ${movieId}`);
                return null;
            }

            // Atualiza os dados
            const movie = this.catalog.movies[movieIndex];
            Object.keys(updateData).forEach(key => {
                if (key !== 'id' && key !== 'dateAdded') { // Protege campos importantes
                    movie[key] = updateData[key];
                }
            });

            movie.lastModified = new Date().toISOString();
            this.saveCatalog();

            console.log(`✅ Filme atualizado: ${movie.title}`);
            return movie;

        } catch (error) {
            console.error(`❌ Erro ao atualizar filme: ${error.message}`);
            throw error;
        }
    }

    /**
     * Adiciona link de download a um filme
     * @param {string} movieId - ID do filme
     * @param {Object} downloadLink - Link de download
     * @returns {boolean} Sucesso da operação
     */
    addDownloadLink(movieId, downloadLink) {
        try {
            const movie = this.getMovieById(movieId);
            if (!movie) {
                console.log(`❌ Filme não encontrado: ${movieId}`);
                return false;
            }

            const link = {
                id: this.generateMovieId(),
                url: downloadLink.url,
                type: downloadLink.type || 'http', // http, torrent, ytdlp
                quality: downloadLink.quality || '',
                size: downloadLink.size || '',
                dateAdded: new Date().toISOString(),
                notes: downloadLink.notes || ''
            };

            movie.downloadLinks.push(link);
            movie.lastModified = new Date().toISOString();
            this.saveCatalog();

            console.log(`✅ Link de download adicionado ao filme: ${movie.title}`);
            return true;

        } catch (error) {
            console.error(`❌ Erro ao adicionar link de download: ${error.message}`);
            return false;
        }
    }

    /**
     * Adiciona arquivo baixado a um filme
     * @param {string} movieId - ID do filme
     * @param {Object} fileInfo - Informações do arquivo
     * @returns {boolean} Sucesso da operação
     */
    addDownloadedFile(movieId, fileInfo) {
        try {
            const movie = this.getMovieById(movieId);
            if (!movie) {
                console.log(`❌ Filme não encontrado: ${movieId}`);
                return false;
            }

            const file = {
                id: this.generateMovieId(),
                fileName: fileInfo.fileName,
                filePath: fileInfo.filePath,
                size: fileInfo.size || 0,
                sizeFormatted: fileInfo.sizeFormatted || '',
                quality: fileInfo.quality || '',
                format: fileInfo.format || '',
                downloadDate: new Date().toISOString(),
                downloadMethod: fileInfo.downloadMethod || 'unknown' // http, torrent, ytdlp
            };

            movie.downloadedFiles.push(file);
            movie.status = 'downloaded';
            movie.lastModified = new Date().toISOString();
            this.saveCatalog();

            console.log(`✅ Arquivo baixado adicionado ao filme: ${movie.title}`);
            return true;

        } catch (error) {
            console.error(`❌ Erro ao adicionar arquivo baixado: ${error.message}`);
            return false;
        }
    }

    /**
     * Remove um filme do catálogo
     * @param {string} movieId - ID do filme
     * @returns {boolean} Sucesso da operação
     */
    removeMovie(movieId) {
        try {
            const movieIndex = this.catalog.movies.findIndex(movie => movie.id === movieId);
            
            if (movieIndex === -1) {
                console.log(`❌ Filme não encontrado: ${movieId}`);
                return false;
            }

            const movie = this.catalog.movies[movieIndex];
            this.catalog.movies.splice(movieIndex, 1);
            this.saveCatalog();

            console.log(`🗑️  Filme removido do catálogo: ${movie.title}`);
            return true;

        } catch (error) {
            console.error(`❌ Erro ao remover filme: ${error.message}`);
            return false;
        }
    }

    /**
     * Obtém estatísticas do catálogo
     * @returns {Object} Estatísticas
     */
    getStats() {
        try {
            const movies = this.catalog.movies;
            const totalMovies = movies.length;
            const downloadedMovies = movies.filter(m => m.status === 'downloaded').length;
            const catalogedMovies = movies.filter(m => m.status === 'cataloged').length;
            const downloadingMovies = movies.filter(m => m.status === 'downloading').length;

            // Gêneros mais comuns
            const genreCount = {};
            movies.forEach(movie => {
                movie.genre.forEach(genre => {
                    genreCount[genre] = (genreCount[genre] || 0) + 1;
                });
            });

            const topGenres = Object.entries(genreCount)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([genre, count]) => ({ genre, count }));

            return {
                totalMovies,
                downloadedMovies,
                catalogedMovies,
                downloadingMovies,
                topGenres,
                catalogSize: this.formatBytes(fs.statSync(this.catalogFile).size),
                lastModified: this.catalog.lastModified
            };

        } catch (error) {
            console.error(`❌ Erro ao obter estatísticas: ${error.message}`);
            return null;
        }
    }

    /**
     * Converte bytes para formato legível
     * @param {number} bytes - Número de bytes
     * @returns {string} Tamanho formatado
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Exporta o catálogo para backup
     * @param {string} backupPath - Caminho para o backup
     * @returns {boolean} Sucesso da operação
     */
    exportCatalog(backupPath) {
        try {
            const backupData = {
                ...this.catalog,
                exportDate: new Date().toISOString(),
                exportVersion: '1.0.0'
            };

            fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2), 'utf8');
            console.log(`💾 Catálogo exportado para: ${backupPath}`);
            return true;

        } catch (error) {
            console.error(`❌ Erro ao exportar catálogo: ${error.message}`);
            return false;
        }
    }
}

module.exports = CatalogManager;
