const axios = require('axios');
const logger = require('../utils/logger');

/**
 * Serviço de Legendas
 * Busca legendas em diferentes provedores
 */
class SubtitleService {
    constructor() {
        this.providers = {
            opensubtitles: {
                baseUrl: 'https://api.opensubtitles.com/api/v1',
                apiKey: process.env.OPENSUBTITLES_API_KEY || null
            },
            subscene: {
                baseUrl: 'https://subscene.com'
            }
        };
        
        this.supportedLanguages = {
            'pt': 'Portuguese',
            'pt-BR': 'Portuguese (Brazil)',
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian'
        };
    }

    /**
     * Busca legendas para um filme
     */
    async searchSubtitles(movieData, languages = ['pt', 'en']) {
        const results = [];

        // Buscar em todos os provedores
        const searchPromises = [];

        if (this.providers.opensubtitles.apiKey) {
            searchPromises.push(this.searchOpenSubtitles(movieData, languages));
        }

        searchPromises.push(this.searchSubscene(movieData, languages));

        const providerResults = await Promise.allSettled(searchPromises);

        providerResults.forEach((result, index) => {
            if (result.status === 'fulfilled' && result.value) {
                results.push(...result.value);
            } else if (result.status === 'rejected') {
                logger.warn('Erro na busca de legendas:', result.reason.message);
            }
        });

        return this.organizeSubtitles(results);
    }

    /**
     * Busca no OpenSubtitles
     */
    async searchOpenSubtitles(movieData, languages) {
        try {
            if (!this.providers.opensubtitles.apiKey) {
                logger.warn('OpenSubtitles API key não configurada');
                return [];
            }

            const subtitles = [];
            
            for (const lang of languages) {
                const params = {
                    languages: lang,
                    moviehash: movieData.hash || undefined,
                    imdb_id: movieData.imdb_id || undefined,
                    tmdb_id: movieData.tmdb_id || undefined,
                    query: movieData.title || undefined,
                    year: movieData.year || undefined
                };

                const response = await axios.get(
                    `${this.providers.opensubtitles.baseUrl}/subtitles`,
                    {
                        params: params,
                        headers: {
                            'Api-Key': this.providers.opensubtitles.apiKey,
                            'User-Agent': 'MovieStash v1.0'
                        },
                        timeout: 10000
                    }
                );

                if (response.data && response.data.data) {
                    const parsed = this.parseOpenSubtitlesResponse(response.data.data, lang);
                    subtitles.push(...parsed);
                }
            }

            return subtitles;
        } catch (error) {
            logger.warn('Erro no OpenSubtitles:', error.message);
            return [];
        }
    }

    /**
     * Processa resposta do OpenSubtitles
     */
    parseOpenSubtitlesResponse(data, language) {
        return data.map(subtitle => ({
            id: subtitle.attributes.subtitle_id,
            language: language,
            languageName: this.supportedLanguages[language] || language,
            filename: subtitle.attributes.files[0]?.file_name || 'Unknown',
            downloadUrl: subtitle.attributes.files[0]?.file_id || null,
            provider: 'OpenSubtitles',
            rating: subtitle.attributes.ratings || 0,
            downloadCount: subtitle.attributes.download_count || 0,
            format: 'srt'
        }));
    }

    /**
     * Busca no Subscene (scraping)
     */
    async searchSubscene(movieData, languages) {
        try {
            // Implementação básica de scraping do Subscene
            const subtitles = [];
            const title = movieData.title || movieData.original_title;
            
            if (!title) {
                return [];
            }

            // Buscar página do filme
            const searchUrl = `${this.providers.subscene.baseUrl}/subtitles/searchbytitle`;
            const searchResponse = await axios.post(searchUrl, {
                query: title,
                l: ''
            }, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                timeout: 10000
            });

            // Parse básico da resposta (seria necessário usar cheerio para parsing completo)
            logger.info(`Busca no Subscene para: ${title}`);
            
            // Por enquanto retorna vazio - implementação completa requer cheerio
            return [];
        } catch (error) {
            logger.warn('Erro no Subscene:', error.message);
            return [];
        }
    }

    /**
     * Organiza legendas por idioma e qualidade
     */
    organizeSubtitles(subtitles) {
        // Agrupar por idioma
        const byLanguage = {};
        
        subtitles.forEach(subtitle => {
            if (!byLanguage[subtitle.language]) {
                byLanguage[subtitle.language] = [];
            }
            byLanguage[subtitle.language].push(subtitle);
        });

        // Ordenar cada idioma por qualidade (rating, downloads)
        Object.keys(byLanguage).forEach(lang => {
            byLanguage[lang].sort((a, b) => {
                // Priorizar por rating
                if (a.rating !== b.rating) {
                    return b.rating - a.rating;
                }
                // Depois por downloads
                return b.downloadCount - a.downloadCount;
            });
        });

        return byLanguage;
    }

    /**
     * Baixa arquivo de legenda
     */
    async downloadSubtitle(subtitleData) {
        try {
            if (subtitleData.provider === 'OpenSubtitles') {
                return await this.downloadFromOpenSubtitles(subtitleData);
            } else if (subtitleData.provider === 'Subscene') {
                return await this.downloadFromSubscene(subtitleData);
            }
            
            throw new Error('Provedor não suportado');
        } catch (error) {
            logger.error('Erro ao baixar legenda:', error.message);
            throw error;
        }
    }

    /**
     * Baixa do OpenSubtitles
     */
    async downloadFromOpenSubtitles(subtitleData) {
        if (!this.providers.opensubtitles.apiKey) {
            throw new Error('OpenSubtitles API key não configurada');
        }

        const response = await axios.post(
            `${this.providers.opensubtitles.baseUrl}/download`,
            {
                file_id: subtitleData.downloadUrl
            },
            {
                headers: {
                    'Api-Key': this.providers.opensubtitles.apiKey,
                    'User-Agent': 'MovieStash v1.0'
                },
                timeout: 15000
            }
        );

        if (response.data && response.data.link) {
            // Baixar o arquivo da URL fornecida
            const fileResponse = await axios.get(response.data.link, {
                responseType: 'text',
                timeout: 30000
            });
            
            return {
                content: fileResponse.data,
                filename: subtitleData.filename,
                format: 'srt'
            };
        }

        throw new Error('Não foi possível obter link de download');
    }

    /**
     * Baixa do Subscene
     */
    async downloadFromSubscene(subtitleData) {
        // Implementação do download do Subscene
        throw new Error('Download do Subscene não implementado ainda');
    }

    /**
     * Converte legenda para formato WebVTT
     */
    convertSrtToVtt(srtContent) {
        try {
            let vttContent = 'WEBVTT\n\n';
            
            // Converter timestamps SRT para VTT
            vttContent += srtContent.replace(
                /(\d{2}):(\d{2}):(\d{2}),(\d{3})/g,
                '$1:$2:$3.$4'
            );
            
            return vttContent;
        } catch (error) {
            logger.error('Erro ao converter SRT para VTT:', error.message);
            throw error;
        }
    }

    /**
     * Valida se a API key do OpenSubtitles está configurada
     */
    isOpenSubtitlesConfigured() {
        return !!this.providers.opensubtitles.apiKey;
    }
}

module.exports = new SubtitleService();
