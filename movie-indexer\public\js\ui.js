/**
 * UI Utilities - Funções utilitárias para interface
 */

class UIUtils {
    constructor() {
        this.toastContainer = document.getElementById('toastContainer');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.loadingText = document.getElementById('loadingText');
    }

    /**
     * Mostra loading
     */
    showLoading(message = 'Carregando...') {
        if (this.loadingText) {
            this.loadingText.textContent = message;
        }
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('active');
        }
    }

    /**
     * Esconde loading
     */
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('active');
        }
    }

    /**
     * Mostra toast notification
     */
    showToast(title, message, type = 'info', duration = 5000) {
        if (!this.toastContainer) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const toastId = this.generateId();
        toast.id = toastId;
        
        toast.innerHTML = `
            <div class="toast-header">
                <span class="toast-title">${this.escapeHtml(title)}</span>
                <button class="toast-close" onclick="ui.removeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-message">${this.escapeHtml(message)}</div>
        `;
        
        this.toastContainer.appendChild(toast);
        
        // Remove automaticamente
        if (duration > 0) {
            setTimeout(() => {
                this.removeToast(toastId);
            }, duration);
        }

        return toastId;
    }

    /**
     * Remove toast
     */
    removeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.style.animation = 'toastSlideOut 0.3s ease forwards';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    /**
     * Mostra modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * Esconde modal
     */
    hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    /**
     * Confirma ação
     */
    confirm(message, title = 'Confirmar') {
        return window.confirm(`${title}\n\n${message}`);
    }

    /**
     * Formata data
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('pt-BR');
        } catch {
            return 'N/A';
        }
    }

    /**
     * Formata duração em minutos
     */
    formatDuration(minutes) {
        if (!minutes) return 'N/A';
        
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    }

    /**
     * Formata tamanho de arquivo
     */
    formatFileSize(bytes) {
        if (!bytes) return 'N/A';
        
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    }

    /**
     * Formata avaliação
     */
    formatRating(rating) {
        if (!rating) return 'N/A';
        return `${rating.toFixed(1)}/10`;
    }

    /**
     * Trunca texto
     */
    truncateText(text, maxLength = 150) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    /**
     * Escapa HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Gera ID único
     */
    generateId() {
        return 'id_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Cria elemento HTML
     */
    createElement(tag, className = '', innerHTML = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }

    /**
     * Obtém parâmetros da URL
     */
    getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        const result = {};
        for (const [key, value] of params) {
            result[key] = value;
        }
        return result;
    }

    /**
     * Atualiza parâmetros da URL
     */
    updateUrlParams(params) {
        const url = new URL(window.location);
        Object.keys(params).forEach(key => {
            if (params[key]) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.history.replaceState({}, '', url);
    }

    /**
     * Copia texto para clipboard
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('Sucesso', 'Texto copiado para a área de transferência', 'success');
            return true;
        } catch (error) {
            console.error('Erro ao copiar:', error);
            this.showToast('Erro', 'Não foi possível copiar o texto', 'error');
            return false;
        }
    }

    /**
     * Abre URL em nova aba
     */
    openInNewTab(url) {
        window.open(url, '_blank', 'noopener,noreferrer');
    }

    /**
     * Valida URL
     */
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Obtém ícone do provedor
     */
    getProviderIcon(provider) {
        const icons = {
            http: 'fas fa-globe',
            gdrive: 'fab fa-google-drive',
            mega: 'fas fa-cloud',
            archive: 'fas fa-archive',
            dropbox: 'fab fa-dropbox',
            mediafire: 'fas fa-fire',
            onedrive: 'fab fa-microsoft'
        };
        return icons[provider] || 'fas fa-link';
    }

    /**
     * Obtém cor do status
     */
    getStatusColor(status) {
        const colors = {
            verified: 'success',
            unverified: 'warning',
            invalid: 'error',
            pending: 'info'
        };
        return colors[status] || 'secondary';
    }

    /**
     * Formata nome do provedor
     */
    formatProviderName(provider) {
        const names = {
            http: 'HTTP',
            gdrive: 'Google Drive',
            mega: 'Mega',
            archive: 'Archive.org',
            dropbox: 'Dropbox',
            mediafire: 'MediaFire',
            onedrive: 'OneDrive'
        };
        return names[provider] || provider.toUpperCase();
    }

    /**
     * Anima elemento
     */
    animate(element, animation, duration = 300) {
        return new Promise(resolve => {
            element.style.animation = `${animation} ${duration}ms ease`;
            setTimeout(() => {
                element.style.animation = '';
                resolve();
            }, duration);
        });
    }

    /**
     * Scroll suave para elemento
     */
    scrollToElement(element, offset = 0) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }

    /**
     * Verifica se elemento está visível
     */
    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
}

// Instância global das utilidades
window.ui = new UIUtils();

// Adicionar animação de saída para toast
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
