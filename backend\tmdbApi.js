const axios = require('axios');

/**
 * Classe para integração com a API do TMDb
 */
class TmdbApi {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://api.themoviedb.org/3';
        this.imageBaseUrl = 'https://image.tmdb.org/t/p';
        this.timeout = 10000; // 10 segundos
    }

    /**
     * Faz requisição para a API do TMDb
     * @param {string} endpoint - Endpoint da API
     * @param {Object} params - Parâmetros da requisição
     * @returns {Promise<Object>} Resposta da API
     */
    async makeRequest(endpoint, params = {}) {
        try {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                params: {
                    api_key: this.apiKey,
                    language: 'pt-BR',
                    ...params
                },
                timeout: this.timeout
            };

            console.log(`🌐 Fazendo requisição para TMDb: ${endpoint}`);
            const response = await axios.get(url, config);
            return response.data;

        } catch (error) {
            console.error(`❌ Erro na requisição TMDb: ${error.message}`);
            if (error.response) {
                console.error(`❌ Status: ${error.response.status}`);
                console.error(`❌ Dados: ${JSON.stringify(error.response.data)}`);
            }
            throw error;
        }
    }

    /**
     * Busca filmes por título
     * @param {string} query - Título do filme
     * @param {number} page - Página dos resultados
     * @returns {Promise<Object>} Resultados da busca
     */
    async searchMovies(query, page = 1) {
        try {
            console.log(`🔍 Buscando filmes: "${query}"`);
            
            const data = await this.makeRequest('/search/movie', {
                query: query,
                page: page,
                include_adult: false
            });

            const movies = data.results.map(movie => this.formatMovieData(movie));
            
            console.log(`✅ Encontrados ${movies.length} filmes na página ${page}`);
            
            return {
                results: movies,
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };

        } catch (error) {
            console.error(`❌ Erro ao buscar filmes: ${error.message}`);
            throw error;
        }
    }

    /**
     * Obtém detalhes de um filme específico
     * @param {number} movieId - ID do filme no TMDb
     * @returns {Promise<Object>} Detalhes do filme
     */
    async getMovieDetails(movieId) {
        try {
            console.log(`🎬 Obtendo detalhes do filme ID: ${movieId}`);
            
            const movie = await this.makeRequest(`/movie/${movieId}`, {
                append_to_response: 'credits,videos,images,keywords'
            });

            const formattedMovie = this.formatDetailedMovieData(movie);
            
            console.log(`✅ Detalhes obtidos: ${formattedMovie.title} (${formattedMovie.year})`);
            return formattedMovie;

        } catch (error) {
            console.error(`❌ Erro ao obter detalhes do filme: ${error.message}`);
            throw error;
        }
    }

    /**
     * Obtém filmes populares
     * @param {number} page - Página dos resultados
     * @returns {Promise<Object>} Filmes populares
     */
    async getPopularMovies(page = 1) {
        try {
            console.log(`🔥 Obtendo filmes populares - página ${page}`);
            
            const data = await this.makeRequest('/movie/popular', { page });
            const movies = data.results.map(movie => this.formatMovieData(movie));
            
            console.log(`✅ ${movies.length} filmes populares obtidos`);
            
            return {
                results: movies,
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };

        } catch (error) {
            console.error(`❌ Erro ao obter filmes populares: ${error.message}`);
            throw error;
        }
    }

    /**
     * Obtém filmes em cartaz
     * @param {number} page - Página dos resultados
     * @returns {Promise<Object>} Filmes em cartaz
     */
    async getNowPlayingMovies(page = 1) {
        try {
            console.log(`🎭 Obtendo filmes em cartaz - página ${page}`);
            
            const data = await this.makeRequest('/movie/now_playing', { page });
            const movies = data.results.map(movie => this.formatMovieData(movie));
            
            console.log(`✅ ${movies.length} filmes em cartaz obtidos`);
            
            return {
                results: movies,
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };

        } catch (error) {
            console.error(`❌ Erro ao obter filmes em cartaz: ${error.message}`);
            throw error;
        }
    }

    /**
     * Obtém gêneros de filmes
     * @returns {Promise<Array>} Lista de gêneros
     */
    async getGenres() {
        try {
            console.log(`🎭 Obtendo lista de gêneros`);
            
            const data = await this.makeRequest('/genre/movie/list');
            
            console.log(`✅ ${data.genres.length} gêneros obtidos`);
            return data.genres;

        } catch (error) {
            console.error(`❌ Erro ao obter gêneros: ${error.message}`);
            throw error;
        }
    }

    /**
     * Formata dados básicos do filme
     * @param {Object} movie - Dados do filme da API
     * @returns {Object} Dados formatados
     */
    formatMovieData(movie) {
        return {
            tmdbId: movie.id,
            title: movie.title,
            originalTitle: movie.original_title,
            year: movie.release_date ? new Date(movie.release_date).getFullYear() : null,
            releaseDate: movie.release_date,
            synopsis: movie.overview,
            poster: movie.poster_path ? this.getImageUrl(movie.poster_path, 'w500') : null,
            posterOriginal: movie.poster_path ? this.getImageUrl(movie.poster_path, 'original') : null,
            backdrop: movie.backdrop_path ? this.getImageUrl(movie.backdrop_path, 'w1280') : null,
            backdropOriginal: movie.backdrop_path ? this.getImageUrl(movie.backdrop_path, 'original') : null,
            genreIds: movie.genre_ids || [],
            rating: movie.vote_average,
            voteCount: movie.vote_count,
            popularity: movie.popularity,
            adult: movie.adult,
            originalLanguage: movie.original_language
        };
    }

    /**
     * Formata dados detalhados do filme
     * @param {Object} movie - Dados detalhados do filme da API
     * @returns {Object} Dados formatados
     */
    formatDetailedMovieData(movie) {
        const basicData = this.formatMovieData(movie);
        
        return {
            ...basicData,
            genre: movie.genres ? movie.genres.map(g => g.name) : [],
            runtime: movie.runtime,
            budget: movie.budget,
            revenue: movie.revenue,
            status: movie.status,
            tagline: movie.tagline,
            homepage: movie.homepage,
            imdbId: movie.imdb_id,
            productionCompanies: movie.production_companies ? 
                movie.production_companies.map(company => ({
                    id: company.id,
                    name: company.name,
                    logo: company.logo_path ? this.getImageUrl(company.logo_path, 'w200') : null
                })) : [],
            productionCountries: movie.production_countries ? 
                movie.production_countries.map(country => country.name) : [],
            spokenLanguages: movie.spoken_languages ? 
                movie.spoken_languages.map(lang => lang.name) : [],
            
            // Elenco e equipe
            cast: movie.credits && movie.credits.cast ? 
                movie.credits.cast.slice(0, 10).map(person => ({
                    id: person.id,
                    name: person.name,
                    character: person.character,
                    profilePath: person.profile_path ? this.getImageUrl(person.profile_path, 'w185') : null
                })) : [],
            
            director: movie.credits && movie.credits.crew ? 
                movie.credits.crew.find(person => person.job === 'Director')?.name || '' : '',
            
            // Vídeos (trailers, etc.)
            videos: movie.videos && movie.videos.results ? 
                movie.videos.results.filter(video => video.site === 'YouTube').map(video => ({
                    id: video.id,
                    key: video.key,
                    name: video.name,
                    type: video.type,
                    url: `https://www.youtube.com/watch?v=${video.key}`
                })) : [],
            
            // Palavras-chave
            keywords: movie.keywords && movie.keywords.keywords ? 
                movie.keywords.keywords.map(keyword => keyword.name) : []
        };
    }

    /**
     * Constrói URL completa para imagens
     * @param {string} imagePath - Caminho da imagem
     * @param {string} size - Tamanho da imagem (w185, w300, w500, w780, w1280, original)
     * @returns {string} URL completa da imagem
     */
    getImageUrl(imagePath, size = 'w500') {
        if (!imagePath) return null;
        return `${this.imageBaseUrl}/${size}${imagePath}`;
    }

    /**
     * Obtém configuração da API (tamanhos de imagem disponíveis, etc.)
     * @returns {Promise<Object>} Configuração da API
     */
    async getConfiguration() {
        try {
            console.log(`⚙️  Obtendo configuração da API`);
            
            const config = await this.makeRequest('/configuration');
            
            console.log(`✅ Configuração obtida`);
            return config;

        } catch (error) {
            console.error(`❌ Erro ao obter configuração: ${error.message}`);
            throw error;
        }
    }

    /**
     * Busca filmes por gênero
     * @param {number} genreId - ID do gênero
     * @param {number} page - Página dos resultados
     * @returns {Promise<Object>} Filmes do gênero
     */
    async getMoviesByGenre(genreId, page = 1) {
        try {
            console.log(`🎭 Buscando filmes do gênero ID: ${genreId}`);
            
            const data = await this.makeRequest('/discover/movie', {
                with_genres: genreId,
                page: page,
                sort_by: 'popularity.desc'
            });

            const movies = data.results.map(movie => this.formatMovieData(movie));
            
            console.log(`✅ ${movies.length} filmes encontrados para o gênero`);
            
            return {
                results: movies,
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };

        } catch (error) {
            console.error(`❌ Erro ao buscar filmes por gênero: ${error.message}`);
            throw error;
        }
    }

    /**
     * Obtém recomendações baseadas em um filme
     * @param {number} movieId - ID do filme
     * @param {number} page - Página dos resultados
     * @returns {Promise<Object>} Filmes recomendados
     */
    async getRecommendations(movieId, page = 1) {
        try {
            console.log(`💡 Obtendo recomendações para filme ID: ${movieId}`);
            
            const data = await this.makeRequest(`/movie/${movieId}/recommendations`, { page });
            const movies = data.results.map(movie => this.formatMovieData(movie));
            
            console.log(`✅ ${movies.length} recomendações obtidas`);
            
            return {
                results: movies,
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };

        } catch (error) {
            console.error(`❌ Erro ao obter recomendações: ${error.message}`);
            throw error;
        }
    }
}

module.exports = TmdbApi;
