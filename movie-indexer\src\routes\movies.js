/**
 * <PERSON>otas de Filmes
 */

const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const Movie = require('../models/Movie');
const MovieLink = require('../models/MovieLink');
const tmdbService = require('../services/tmdbService');
const linkValidator = require('../services/linkValidator');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Middleware de validação
 */
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Dados inválidos',
            details: errors.array()
        });
    }
    next();
};

/**
 * GET /api/movies
 * Lista filmes com filtros e paginação
 */
router.get('/', [
    query('page').optional().isInt({ min: 1 }).withMessage('Página deve ser um número positivo'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite deve ser entre 1 e 100'),
    query('search').optional().isLength({ min: 1, max: 100 }).withMessage('Busca deve ter entre 1 e 100 caracteres'),
    query('genre').optional().isLength({ min: 1, max: 50 }).withMessage('Gênero inválido'),
    query('year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Ano inválido'),
    query('resolution').optional().isIn(['480p', '720p', '1080p', '4k']).withMessage('Resolução inválida'),
    query('watched').optional().isBoolean().withMessage('Watched deve ser boolean'),
    query('favorite').optional().isBoolean().withMessage('Favorite deve ser boolean'),
    query('sortBy').optional().isIn(['title', 'year', 'created_at', 'vote_average', 'popularity']).withMessage('Campo de ordenação inválido'),
    query('sortOrder').optional().isIn(['ASC', 'DESC']).withMessage('Ordem deve ser ASC ou DESC')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 20,
        search: req.query.search || '',
        genre: req.query.genre || '',
        year: req.query.year || '',
        resolution: req.query.resolution || '',
        watched: req.query.watched !== undefined ? req.query.watched === 'true' : null,
        favorite: req.query.favorite !== undefined ? req.query.favorite === 'true' : null,
        sortBy: req.query.sortBy || 'created_at',
        sortOrder: req.query.sortOrder || 'DESC'
    };

    const result = await Movie.findAll(options);
    
    // Adicionar links para cada filme
    for (const movie of result.movies) {
        movie.links = await MovieLink.findByMovieId(movie.id);
    }

    logger.activity('list', 'movies', { filters: options }, req.ip);

    res.json({
        success: true,
        data: result.movies,
        pagination: result.pagination
    });
}));

/**
 * GET /api/movies/recent
 * Lista filmes recentes
 */
router.get('/recent', [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limite deve ser entre 1 e 50')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const limit = parseInt(req.query.limit) || 10;
    const movies = await Movie.findRecent(limit);
    
    // Adicionar links para cada filme
    for (const movie of movies) {
        movie.links = await MovieLink.findByMovieId(movie.id);
    }

    res.json({
        success: true,
        data: movies
    });
}));

/**
 * GET /api/movies/popular
 * Lista filmes populares
 */
router.get('/popular', [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limite deve ser entre 1 e 50')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const limit = parseInt(req.query.limit) || 10;
    const movies = await Movie.findPopular(limit);
    
    // Adicionar links para cada filme
    for (const movie of movies) {
        movie.links = await MovieLink.findByMovieId(movie.id);
    }

    res.json({
        success: true,
        data: movies
    });
}));

/**
 * GET /api/movies/:id
 * Obtém detalhes de um filme
 */
router.get('/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movie = await Movie.findById(req.params.id);
    
    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    // Adicionar links do filme
    movie.links = await MovieLink.findByMovieId(movie.id);

    logger.activity('view', 'movie', { movieId: movie.id, title: movie.title }, req.ip);

    res.json({
        success: true,
        data: movie
    });
}));

/**
 * POST /api/movies
 * Cria um novo filme
 */
router.post('/', [
    // Título é opcional se tmdb_id for fornecido
    body('title').optional().isLength({ min: 1, max: 255 }).withMessage('Título deve ter até 255 caracteres'),
    body('tmdb_id').optional().isInt({ min: 1 }).withMessage('TMDb ID deve ser um número positivo'),
    body('year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Ano inválido'),
    body('overview').optional().isLength({ max: 2000 }).withMessage('Sinopse deve ter até 2000 caracteres'),
    body('user_rating').optional().isInt({ min: 1, max: 10 }).withMessage('Avaliação deve ser entre 1 e 10'),
    body('user_notes').optional().isLength({ max: 1000 }).withMessage('Notas devem ter até 1000 caracteres'),
    body('watched').optional().isBoolean().withMessage('Watched deve ser boolean'),
    body('favorite').optional().isBoolean().withMessage('Favorite deve ser boolean')
], handleValidationErrors, asyncHandler(async (req, res) => {
    // Validar que pelo menos title ou tmdb_id seja fornecido
    if (!req.body.title && !req.body.tmdb_id) {
        return res.status(400).json({
            success: false,
            error: 'Título ou TMDb ID deve ser fornecido'
        });
    }

    // Se TMDb ID fornecido, buscar dados completos
    if (req.body.tmdb_id) {
        try {
            // Verificar se filme já existe
            const existingMovie = await Movie.findByTmdbId(req.body.tmdb_id);
            if (existingMovie) {
                logger.info(`Filme já existe no catálogo: ${existingMovie.title} (TMDb ID: ${req.body.tmdb_id})`);
                return res.status(200).json({
                    success: true,
                    data: existingMovie,
                    message: 'Filme já existe no catálogo',
                    existing: true
                });
            }

            const tmdbData = await tmdbService.getMovieDetails(req.body.tmdb_id);

            // Mesclar dados do TMDb com dados do usuário
            const movieData = {
                ...tmdbData,
                ...req.body, // Dados do usuário sobrescrevem TMDb
                tmdb_id: req.body.tmdb_id
            };

            const movie = new Movie(movieData);
            await movie.save();

            logger.activity('create', 'movie', { movieId: movie.id, title: movie.title, source: 'tmdb' }, req.ip);

            return res.status(201).json({
                success: true,
                data: movie,
                message: 'Filme criado com dados do TMDb'
            });
        } catch (error) {
            logger.warn('Erro ao buscar dados do TMDb:', error.message);

            // Se erro for de constraint, verificar se filme já existe
            if (error.message && error.message.includes('UNIQUE constraint failed: movies.tmdb_id')) {
                const existingMovie = await Movie.findByTmdbId(req.body.tmdb_id);
                if (existingMovie) {
                    return res.status(200).json({
                        success: true,
                        data: existingMovie,
                        message: 'Filme já existe no catálogo',
                        existing: true
                    });
                }
            }

            // Se não conseguir buscar do TMDb, tentar criar apenas com dados fornecidos
            if (!req.body.title) {
                return res.status(400).json({
                    success: false,
                    error: 'Não foi possível buscar dados do TMDb e título não foi fornecido'
                });
            }
        }
    }

    // Criar filme apenas com dados fornecidos
    const movie = new Movie(req.body);
    await movie.save();
    
    logger.activity('create', 'movie', { movieId: movie.id, title: movie.title, source: 'manual' }, req.ip);

    res.status(201).json({
        success: true,
        data: movie,
        message: 'Filme criado com sucesso'
    });
}));

/**
 * PUT /api/movies/:id
 * Atualiza um filme
 */
router.put('/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo'),
    body('title').optional().isLength({ min: 1, max: 255 }).withMessage('Título deve ter até 255 caracteres'),
    body('year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Ano inválido'),
    body('overview').optional().isLength({ max: 2000 }).withMessage('Sinopse deve ter até 2000 caracteres'),
    body('user_rating').optional().isInt({ min: 1, max: 10 }).withMessage('Avaliação deve ser entre 1 e 10'),
    body('user_notes').optional().isLength({ max: 1000 }).withMessage('Notas devem ter até 1000 caracteres'),
    body('watched').optional().isBoolean().withMessage('Watched deve ser boolean'),
    body('favorite').optional().isBoolean().withMessage('Favorite deve ser boolean')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movie = await Movie.findById(req.params.id);
    
    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    // Atualizar propriedades
    Object.keys(req.body).forEach(key => {
        if (req.body[key] !== undefined) {
            movie[key] = req.body[key];
        }
    });

    await movie.save();
    
    logger.activity('update', 'movie', { movieId: movie.id, title: movie.title }, req.ip);

    res.json({
        success: true,
        data: movie,
        message: 'Filme atualizado com sucesso'
    });
}));

/**
 * DELETE /api/movies/:id
 * Remove um filme
 */
router.delete('/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movie = await Movie.findById(req.params.id);
    
    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    await movie.delete();
    
    logger.activity('delete', 'movie', { movieId: movie.id, title: movie.title }, req.ip);

    res.json({
        success: true,
        message: 'Filme removido com sucesso'
    });
}));

/**
 * POST /api/movies/:id/links
 * Adiciona link a um filme
 */
router.post('/:id/links', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo'),
    body('url').isURL().withMessage('URL inválida'),
    body('resolution').isIn(['480p', '720p', '1080p', '4k']).withMessage('Resolução inválida'),
    body('provider').optional().isLength({ min: 1, max: 50 }).withMessage('Provedor inválido'),
    body('quality').optional().isLength({ max: 50 }).withMessage('Qualidade inválida'),
    body('language').optional().isLength({ max: 10 }).withMessage('Idioma inválido'),
    body('user_notes').optional().isLength({ max: 500 }).withMessage('Notas devem ter até 500 caracteres')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movie = await Movie.findById(req.params.id);
    
    if (!movie) {
        return res.status(404).json({
            success: false,
            error: 'Filme não encontrado'
        });
    }

    // Detectar provedor automaticamente se não fornecido
    const provider = req.body.provider || linkValidator.detectProvider(req.body.url);

    const linkData = {
        movie_id: movie.id,
        url: req.body.url,
        provider: provider,
        resolution: req.body.resolution,
        quality: req.body.quality || '',
        language: req.body.language || 'pt-BR',
        user_notes: req.body.user_notes || ''
    };

    const link = new MovieLink(linkData);
    await link.save();

    // Validar link em background (não bloquear resposta)
    linkValidator.validateLink(link.url, link.provider)
        .then(result => {
            link.updateVerificationStatus(result.isValid, result.error || '');
        })
        .catch(error => {
            logger.error('Erro na validação de link em background:', error.message);
        });

    logger.activity('create', 'link', { linkId: link.id, movieId: movie.id, provider }, req.ip);

    res.status(201).json({
        success: true,
        data: link,
        message: 'Link adicionado com sucesso'
    });
}));

module.exports = router;
