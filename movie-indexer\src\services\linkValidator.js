/**
 * Serviço de Validação de Links
 * Valida se links são públicos e acessíveis para diferentes provedores
 */

const axios = require('axios');
const logger = require('../utils/logger');

class LinkValidator {
    constructor() {
        this.timeout = parseInt(process.env.LINK_VALIDATION_TIMEOUT) || 10000;
        this.retries = parseInt(process.env.LINK_VALIDATION_RETRIES) || 3;
        this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    }

    /**
     * Valida um link baseado no provedor
     */
    async validateLink(url, provider = null) {
        try {
            if (!url || typeof url !== 'string') {
                return { isValid: false, error: 'URL inválida' };
            }

            // Detectar provedor automaticamente se não fornecido
            if (!provider) {
                provider = this.detectProvider(url);
            }

            logger.debug(`Validando link: ${url} (Provedor: ${provider})`);

            // Validar baseado no provedor
            switch (provider) {
                case 'gdrive':
                    return await this.validateGoogleDrive(url);
                case 'mega':
                    return await this.validateMega(url);
                case 'archive':
                    return await this.validateArchiveOrg(url);
                case 'dropbox':
                    return await this.validateDropbox(url);
                case 'onedrive':
                    return await this.validateOneDrive(url);
                case 'mediafire':
                    return await this.validateMediaFire(url);
                case 'http':
                default:
                    return await this.validateHttp(url);
            }
        } catch (error) {
            logger.error('Erro na validação de link:', { error: error.message, url });
            return { isValid: false, error: error.message };
        }
    }

    /**
     * Detecta o provedor baseado na URL
     */
    detectProvider(url) {
        const urlLower = url.toLowerCase();

        if (urlLower.includes('drive.google.com') || urlLower.includes('docs.google.com')) {
            return 'gdrive';
        }
        if (urlLower.includes('mega.nz') || urlLower.includes('mega.co.nz')) {
            return 'mega';
        }
        if (urlLower.includes('archive.org')) {
            return 'archive';
        }
        if (urlLower.includes('dropbox.com')) {
            return 'dropbox';
        }
        if (urlLower.includes('1drv.ms') || urlLower.includes('onedrive.live.com')) {
            return 'onedrive';
        }
        if (urlLower.includes('mediafire.com')) {
            return 'mediafire';
        }

        return 'http';
    }

    /**
     * Valida links do Google Drive
     */
    async validateGoogleDrive(url) {
        try {
            // Extrair ID do arquivo do Google Drive
            const fileIdMatch = url.match(/\/d\/([a-zA-Z0-9-_]+)/);
            if (!fileIdMatch) {
                return { isValid: false, error: 'ID do arquivo Google Drive não encontrado' };
            }

            const fileId = fileIdMatch[1];
            
            // Verificar se o arquivo é público
            const checkUrl = `https://drive.google.com/file/d/${fileId}/view`;
            
            const response = await this.makeRequest(checkUrl, {
                method: 'HEAD',
                timeout: this.timeout
            });

            if (response.status === 200) {
                return { 
                    isValid: true, 
                    provider: 'gdrive',
                    fileId: fileId,
                    directUrl: `https://drive.google.com/uc?export=download&id=${fileId}`
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro Google Drive: ${error.message}` };
        }
    }

    /**
     * Valida links do Mega
     */
    async validateMega(url) {
        try {
            // Verificar formato da URL do Mega
            if (!url.match(/mega\.nz\/(file|#!)/)) {
                return { isValid: false, error: 'Formato de URL Mega inválido' };
            }

            // Para Mega, apenas verificamos se a URL responde
            const response = await this.makeRequest(url, {
                method: 'HEAD',
                timeout: this.timeout
            });

            if (response.status === 200) {
                return { 
                    isValid: true, 
                    provider: 'mega',
                    note: 'Validação básica - verificação completa requer API do Mega'
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro Mega: ${error.message}` };
        }
    }

    /**
     * Valida links do Archive.org
     */
    async validateArchiveOrg(url) {
        try {
            const response = await this.makeRequest(url, {
                method: 'HEAD',
                timeout: this.timeout
            });

            if (response.status === 200) {
                return { 
                    isValid: true, 
                    provider: 'archive',
                    contentType: response.headers['content-type'],
                    contentLength: response.headers['content-length']
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro Archive.org: ${error.message}` };
        }
    }

    /**
     * Valida links do Dropbox
     */
    async validateDropbox(url) {
        try {
            // Converter URL de visualização para download direto
            let directUrl = url;
            if (url.includes('dropbox.com') && !url.includes('dl=1')) {
                directUrl = url.replace('dl=0', 'dl=1');
                if (!directUrl.includes('dl=1')) {
                    directUrl += (url.includes('?') ? '&' : '?') + 'dl=1';
                }
            }

            const response = await this.makeRequest(directUrl, {
                method: 'HEAD',
                timeout: this.timeout
            });

            if (response.status === 200) {
                return { 
                    isValid: true, 
                    provider: 'dropbox',
                    directUrl: directUrl,
                    contentType: response.headers['content-type'],
                    contentLength: response.headers['content-length']
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro Dropbox: ${error.message}` };
        }
    }

    /**
     * Valida links do OneDrive
     */
    async validateOneDrive(url) {
        try {
            const response = await this.makeRequest(url, {
                method: 'HEAD',
                timeout: this.timeout
            });

            if (response.status === 200) {
                return { 
                    isValid: true, 
                    provider: 'onedrive',
                    note: 'Link OneDrive válido'
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro OneDrive: ${error.message}` };
        }
    }

    /**
     * Valida links do MediaFire
     */
    async validateMediaFire(url) {
        try {
            const response = await this.makeRequest(url, {
                method: 'GET',
                timeout: this.timeout
            });

            if (response.status === 200) {
                // Verificar se não é página de erro do MediaFire
                const content = response.data;
                if (content.includes('File not found') || content.includes('Invalid or Deleted File')) {
                    return { isValid: false, error: 'Arquivo não encontrado no MediaFire' };
                }

                return { 
                    isValid: true, 
                    provider: 'mediafire'
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro MediaFire: ${error.message}` };
        }
    }

    /**
     * Valida links HTTP genéricos
     */
    async validateHttp(url) {
        try {
            const response = await this.makeRequest(url, {
                method: 'HEAD',
                timeout: this.timeout
            });

            if (response.status >= 200 && response.status < 400) {
                return { 
                    isValid: true, 
                    provider: 'http',
                    contentType: response.headers['content-type'],
                    contentLength: response.headers['content-length'],
                    lastModified: response.headers['last-modified']
                };
            } else {
                return { isValid: false, error: `Status HTTP: ${response.status}` };
            }
        } catch (error) {
            return { isValid: false, error: `Erro HTTP: ${error.message}` };
        }
    }

    /**
     * Faz requisição HTTP com retry
     */
    async makeRequest(url, options = {}) {
        const defaultOptions = {
            method: 'HEAD',
            timeout: this.timeout,
            headers: {
                'User-Agent': this.userAgent,
                'Accept': '*/*',
                'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache'
            },
            maxRedirects: 5,
            validateStatus: (status) => status < 500 // Não rejeitar para status 4xx
        };

        const config = { ...defaultOptions, ...options };

        for (let attempt = 1; attempt <= this.retries; attempt++) {
            try {
                logger.debug(`Tentativa ${attempt}/${this.retries} para ${url}`);
                
                const response = await axios({
                    url,
                    ...config
                });

                return response;
            } catch (error) {
                logger.debug(`Tentativa ${attempt} falhou:`, error.message);
                
                if (attempt === this.retries) {
                    throw error;
                }
                
                // Aguardar antes da próxima tentativa
                await this.sleep(1000 * attempt);
            }
        }
    }

    /**
     * Valida múltiplos links em lote
     */
    async validateBatch(links) {
        const results = [];
        
        for (const link of links) {
            try {
                const result = await this.validateLink(link.url, link.provider);
                results.push({
                    id: link.id,
                    url: link.url,
                    ...result
                });
            } catch (error) {
                results.push({
                    id: link.id,
                    url: link.url,
                    isValid: false,
                    error: error.message
                });
            }
            
            // Pequena pausa entre validações para não sobrecarregar
            await this.sleep(500);
        }
        
        return results;
    }

    /**
     * Verifica se uma URL é válida
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Extrai informações básicas de uma URL
     */
    extractUrlInfo(url) {
        try {
            const urlObj = new URL(url);
            return {
                protocol: urlObj.protocol,
                hostname: urlObj.hostname,
                pathname: urlObj.pathname,
                search: urlObj.search,
                provider: this.detectProvider(url)
            };
        } catch (error) {
            return null;
        }
    }

    /**
     * Pausa execução por um tempo
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtém estatísticas de validação
     */
    async getValidationStats() {
        // Esta função seria implementada para retornar estatísticas
        // de validações realizadas, taxa de sucesso, etc.
        return {
            totalValidations: 0,
            successRate: 0,
            providerStats: {}
        };
    }
}

module.exports = new LinkValidator();
