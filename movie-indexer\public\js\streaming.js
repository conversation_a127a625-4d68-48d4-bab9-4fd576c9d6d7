/**
 * Sistema de Streaming com WebTorrent
 * Gerencia streaming de torrents e legendas
 */
class StreamingManager {
    constructor() {
        this.client = null;
        this.currentTorrent = null;
        this.currentMovie = null;
        this.subtitles = {};
        this.isInitialized = false;
    }

    /**
     * Inicializa o WebTorrent
     */
    initialize() {
        if (this.isInitialized) return;

        try {
            // Configurações otimizadas para WebTorrent
            this.client = new WebTorrent({
                tracker: {
                    announce: [
                        'wss://tracker.btorrent.xyz',
                        'wss://tracker.openwebtorrent.com',
                        'wss://tracker.fastcast.nz',
                        'wss://tracker.webtorrent.io'
                    ]
                },
                dht: true,
                webSeeds: true
            });

            this.client.on('error', (err) => {
                console.error('Erro no cliente WebTorrent:', err);
            });

            this.client.on('warning', (err) => {
                console.warn('Aviso do WebTorrent:', err);
            });

            this.isInitialized = true;
            console.log('WebTorrent inicializado com configurações otimizadas');
        } catch (error) {
            console.error('Erro ao inicializar WebTorrent:', error);
        }
    }

    /**
     * Busca opções de streaming para um filme
     */
    async getStreamingOptions(movieId) {
        try {
            const response = await api.get(`/api/streaming/options/${movieId}`);
            return response;
        } catch (error) {
            console.error('Erro ao buscar opções de streaming:', error);
            throw error;
        }
    }

    /**
     * Busca legendas para um filme
     */
    async getSubtitles(movieId, languages = ['pt', 'en']) {
        try {
            const response = await api.get(`/api/streaming/subtitles/${movieId}?languages=${languages.join(',')}`);
            return response.subtitles || {};
        } catch (error) {
            console.error('Erro ao buscar legendas:', error);
            return {};
        }
    }

    /**
     * Inicia streaming de um torrent
     */
    async startStream(magnetLink, options = {}) {
        // Verificar se é uma URL HTTP direta
        if (magnetLink.startsWith('http://') || magnetLink.startsWith('https://')) {
            console.log('Detectada URL HTTP direta, usando player direto');
            return this.startDirectStream(magnetLink, options);
        }

        if (!this.isInitialized) {
            this.initialize();
        }

        // Para teste, usar um torrent conhecido que funciona
        const testMode = true; // Ativado para teste
        if (testMode) {
            magnetLink = 'magnet:?xt=urn:btih:08ada5a7a6183aae1e09d831df6748d566095a10&dn=Sintel&tr=udp%3A%2F%2Fexplodie.org%3A6969&tr=udp%3A%2F%2Ftracker.coppersurfer.tk%3A6969&tr=udp%3A%2F%2Ftracker.empire-js.us%3A1337&tr=udp%3A%2F%2Ftracker.leechers-paradise.org%3A6969&tr=udp%3A%2F%2Ftracker.opentrackr.org%3A1337&tr=wss%3A%2F%2Ftracker.btorrent.xyz&tr=wss%3A%2F%2Ftracker.fastcast.nz&tr=wss%3A%2F%2Ftracker.openwebtorrent.com';
            console.log('Usando torrent de teste (Sintel)');
        }

        return new Promise((resolve, reject) => {
            try {
                // Limpar torrent anterior se existir
                if (this.currentTorrent) {
                    this.currentTorrent.destroy();
                }

                console.log('Iniciando stream:', magnetLink);
                console.log('Cliente WebTorrent status:', {
                    torrents: this.client.torrents.length,
                    downloadSpeed: this.client.downloadSpeed,
                    uploadSpeed: this.client.uploadSpeed
                });

                this.currentTorrent = this.client.add(magnetLink, {
                    path: '/tmp/webtorrent/',
                    announce: [
                        'wss://tracker.btorrent.xyz',
                        'wss://tracker.openwebtorrent.com',
                        'wss://tracker.fastcast.nz'
                    ]
                });

                // Logs de debug
                this.currentTorrent.on('infoHash', () => {
                    console.log('InfoHash obtido:', this.currentTorrent.infoHash);
                });

                this.currentTorrent.on('metadata', () => {
                    console.log('Metadata recebida');
                });

                this.currentTorrent.on('wire', (wire) => {
                    console.log('Nova conexão peer:', wire.remoteAddress);
                });

                this.currentTorrent.on('download', (bytes) => {
                    console.log('Download iniciado, bytes:', bytes);
                });

                this.currentTorrent.on('ready', () => {
                    console.log('Torrent pronto:', this.currentTorrent.name);
                    console.log('Arquivos no torrent:', this.currentTorrent.files.map(f => f.name));

                    // Encontrar o maior arquivo de vídeo
                    const videoFile = this.findVideoFile(this.currentTorrent.files);

                    if (videoFile) {
                        console.log('Arquivo de vídeo encontrado:', videoFile.name, 'Tamanho:', videoFile.length);
                        resolve({
                            success: true,
                            torrent: this.currentTorrent,
                            file: videoFile,
                            name: this.currentTorrent.name
                        });
                    } else {
                        console.error('Nenhum arquivo de vídeo encontrado nos arquivos:', this.currentTorrent.files.map(f => f.name));
                        reject(new Error('Nenhum arquivo de vídeo encontrado'));
                    }
                });

                this.currentTorrent.on('error', (error) => {
                    console.error('Erro no torrent:', error);
                    reject(error);
                });

                // Timeout de 30 segundos (reduzido para fallback mais rápido)
                const timeoutId = setTimeout(() => {
                    if (!this.currentTorrent.ready) {
                        console.error('Timeout: Torrent não ficou pronto em 30 segundos');
                        console.log('Status do torrent:', {
                            ready: this.currentTorrent.ready,
                            numPeers: this.currentTorrent.numPeers,
                            downloaded: this.currentTorrent.downloaded,
                            progress: this.currentTorrent.progress
                        });

                        // Tentar fallback para URL direta se disponível
                        console.log('Tentando fallback para stream direto...');
                        this.tryFallbackStream(magnetLink, options)
                            .then(resolve)
                            .catch(() => {
                                reject(new Error('Timeout ao iniciar torrent - sem peers ou metadata'));
                            });
                    }
                }, 30000);

                // Limpar timeout quando torrent estiver pronto
                this.currentTorrent.on('ready', () => {
                    clearTimeout(timeoutId);
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Inicia streaming direto de URL HTTP
     */
    async startDirectStream(url, options = {}) {
        console.log('Iniciando stream direto de URL:', url);

        return new Promise((resolve, reject) => {
            try {
                // Simular um objeto "file" para compatibilidade
                const fakeFile = {
                    name: 'video.mp4',
                    length: 0,
                    getBlobURL: (callback) => {
                        // Para URLs diretas, retornar a própria URL
                        callback(null, url);
                    }
                };

                // Simular um objeto "torrent" para compatibilidade
                const fakeTorrent = {
                    name: 'Direct Stream',
                    downloadSpeed: 0,
                    uploadSpeed: 0,
                    progress: 1,
                    numPeers: 0,
                    downloaded: 0,
                    uploaded: 0
                };

                resolve({
                    success: true,
                    torrent: fakeTorrent,
                    file: fakeFile,
                    name: 'Direct Stream'
                });

            } catch (error) {
                console.error('Erro no stream direto:', error);
                reject(error);
            }
        });
    }

    /**
     * Tenta fallback para streaming quando torrent falha
     */
    async tryFallbackStream(magnetLink, options = {}) {
        console.log('Tentando métodos alternativos de streaming...');

        // Método 1: Tentar extrair URL de streaming do magnet
        const streamUrl = this.extractStreamUrlFromMagnet(magnetLink);
        if (streamUrl) {
            console.log('URL de stream extraída:', streamUrl);
            return this.startDirectStream(streamUrl, options);
        }

        // Método 2: Usar URL de demonstração para teste
        const demoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
        console.log('Usando vídeo de demonstração para teste');
        return this.startDirectStream(demoUrl, options);
    }

    /**
     * Tenta extrair URL de streaming de um magnet link
     */
    extractStreamUrlFromMagnet(magnetLink) {
        // Esta é uma função placeholder - na prática, você precisaria
        // de um serviço que converta magnet links para URLs de streaming
        // Por exemplo: WebTorrent.io, Instant.io, etc.

        // Para demonstração, retornar null (sem URL encontrada)
        return null;
    }

    /**
     * Encontra o arquivo de vídeo principal
     */
    findVideoFile(files) {
        const videoExtensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'];

        console.log('Procurando arquivos de vídeo em:', files.map(f => ({ name: f.name, size: f.length })));

        // Filtrar apenas arquivos de vídeo
        const videoFiles = files.filter(file => {
            const fileName = file.name.toLowerCase();
            const hasVideoExtension = videoExtensions.some(ext => fileName.endsWith(ext));
            const isLargeEnough = file.length > 50 * 1024 * 1024; // Maior que 50MB

            console.log(`Arquivo: ${file.name}, É vídeo: ${hasVideoExtension}, Grande o suficiente: ${isLargeEnough}`);

            return hasVideoExtension && isLargeEnough;
        });

        console.log('Arquivos de vídeo encontrados:', videoFiles.map(f => f.name));

        if (videoFiles.length === 0) {
            // Se não encontrou vídeos grandes, tentar qualquer arquivo de vídeo
            const anyVideoFiles = files.filter(file => {
                const fileName = file.name.toLowerCase();
                return videoExtensions.some(ext => fileName.endsWith(ext));
            });

            if (anyVideoFiles.length > 0) {
                console.log('Usando qualquer arquivo de vídeo:', anyVideoFiles[0].name);
                return anyVideoFiles.reduce((largest, file) => {
                    return file.length > largest.length ? file : largest;
                });
            }

            return null;
        }

        // Retornar o maior arquivo
        const largestFile = videoFiles.reduce((largest, file) => {
            return file.length > largest.length ? file : largest;
        });

        console.log('Arquivo de vídeo selecionado:', largestFile.name);
        return largestFile;
    }

    /**
     * Para o streaming atual
     */
    stopStream() {
        if (this.currentTorrent) {
            this.currentTorrent.destroy();
            this.currentTorrent = null;
            console.log('Stream parado');
        }
    }

    /**
     * Obtém estatísticas do stream atual
     */
    getStreamStats() {
        if (!this.currentTorrent) return null;

        return {
            downloadSpeed: this.formatBytes(this.currentTorrent.downloadSpeed) + '/s',
            uploadSpeed: this.formatBytes(this.currentTorrent.uploadSpeed) + '/s',
            progress: Math.round(this.currentTorrent.progress * 100) + '%',
            peers: this.currentTorrent.numPeers,
            downloaded: this.formatBytes(this.currentTorrent.downloaded),
            uploaded: this.formatBytes(this.currentTorrent.uploaded)
        };
    }

    /**
     * Formata bytes para exibição
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Baixa uma legenda
     */
    async downloadSubtitle(subtitleData) {
        try {
            const response = await api.post('/api/streaming/subtitles/download', subtitleData);
            return response;
        } catch (error) {
            console.error('Erro ao baixar legenda:', error);
            throw error;
        }
    }

    /**
     * Carrega legenda no player
     */
    loadSubtitle(videoElement, subtitleContent, language) {
        try {
            // Remover track anterior se existir
            const existingTrack = videoElement.querySelector('track');
            if (existingTrack) {
                existingTrack.remove();
            }

            // Criar blob da legenda
            const blob = new Blob([subtitleContent], { type: 'text/vtt' });
            const url = URL.createObjectURL(blob);

            // Criar nova track
            const track = document.createElement('track');
            track.kind = 'subtitles';
            track.label = language;
            track.srclang = language.split('-')[0];
            track.src = url;
            track.default = true;

            videoElement.appendChild(track);
            
            // Ativar a track
            if (videoElement.textTracks.length > 0) {
                videoElement.textTracks[0].mode = 'showing';
            }

            console.log('Legenda carregada:', language);
        } catch (error) {
            console.error('Erro ao carregar legenda:', error);
        }
    }

    /**
     * Limpa recursos
     */
    cleanup() {
        if (this.currentTorrent) {
            this.currentTorrent.destroy();
        }
        if (this.client) {
            this.client.destroy();
        }
        this.isInitialized = false;
    }
}

// Instância global do streaming manager
const streamingManager = new StreamingManager();

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    streamingManager.initialize();
});

// Limpar recursos quando a página for fechada
window.addEventListener('beforeunload', () => {
    streamingManager.cleanup();
});
