{"name": "movie-stash", "version": "1.0.0", "description": "Aplicativo desktop para catalogar e baixar filmes localmente", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "test": "node backend/index.js"}, "keywords": ["electron", "movies", "catalog", "downloader", "tmdb"], "author": "Movie Stash", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.6.0", "webtorrent": "^1.9.7", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "progress": "^2.0.3"}, "build": {"appId": "com.moviestash.app", "productName": "Movie Stash", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}