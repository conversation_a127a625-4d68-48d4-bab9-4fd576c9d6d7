/**
 * Modelo de Link de Filme
 */

const database = require('../config/database');
const logger = require('../utils/logger');

class MovieLink {
    constructor(data = {}) {
        this.id = data.id || null;
        this.movie_id = data.movie_id || null;
        this.url = data.url || '';
        this.provider = data.provider || 'http';
        this.resolution = data.resolution || '1080p';
        this.quality = data.quality || '';
        this.size_mb = data.size_mb || null;
        this.language = data.language || 'pt-BR';
        this.subtitle_language = data.subtitle_language || '';
        this.audio_format = data.audio_format || '';
        this.video_format = data.video_format || '';
        this.is_public = data.is_public !== undefined ? data.is_public : true;
        this.is_verified = data.is_verified || false;
        this.last_verified = data.last_verified || null;
        this.verification_status = data.verification_status || 'pending';
        this.verification_error = data.verification_error || '';
        this.download_count = data.download_count || 0;
        this.stream_count = data.stream_count || 0;
        this.user_notes = data.user_notes || '';
        this.status = data.status || 'active';
        this.created_at = data.created_at || null;
        this.updated_at = data.updated_at || null;
    }

    /**
     * Salva o link no banco de dados
     */
    async save() {
        try {
            const now = new Date().toISOString();
            
            if (this.id) {
                // Atualizar link existente
                this.updated_at = now;
                await database.run(`
                    UPDATE movie_links SET
                        movie_id = ?, url = ?, provider = ?, resolution = ?, quality = ?,
                        size_mb = ?, language = ?, subtitle_language = ?, audio_format = ?,
                        video_format = ?, is_public = ?, is_verified = ?, last_verified = ?,
                        verification_status = ?, verification_error = ?, download_count = ?,
                        stream_count = ?, user_notes = ?, status = ?, updated_at = ?
                    WHERE id = ?
                `, [
                    this.movie_id, this.url, this.provider, this.resolution, this.quality,
                    this.size_mb, this.language, this.subtitle_language, this.audio_format,
                    this.video_format, this.is_public, this.is_verified, this.last_verified,
                    this.verification_status, this.verification_error, this.download_count,
                    this.stream_count, this.user_notes, this.status, this.updated_at, this.id
                ]);
                
                logger.info(`Link atualizado: ${this.provider} ${this.resolution} (ID: ${this.id})`);
                return this;
            } else {
                // Criar novo link
                this.created_at = now;
                this.updated_at = now;
                
                const result = await database.run(`
                    INSERT INTO movie_links (
                        movie_id, url, provider, resolution, quality, size_mb, language,
                        subtitle_language, audio_format, video_format, is_public, is_verified,
                        last_verified, verification_status, verification_error, download_count,
                        stream_count, user_notes, status, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    this.movie_id, this.url, this.provider, this.resolution, this.quality,
                    this.size_mb, this.language, this.subtitle_language, this.audio_format,
                    this.video_format, this.is_public, this.is_verified, this.last_verified,
                    this.verification_status, this.verification_error, this.download_count,
                    this.stream_count, this.user_notes, this.status, this.created_at, this.updated_at
                ]);
                
                this.id = result.id;
                logger.info(`Link criado: ${this.provider} ${this.resolution} (ID: ${this.id})`);
                return this;
            }
        } catch (error) {
            logger.error('Erro ao salvar link:', { error: error.message, url: this.url });
            throw error;
        }
    }

    /**
     * Busca link por ID
     */
    static async findById(id) {
        try {
            const row = await database.get('SELECT * FROM movie_links WHERE id = ? AND status != "deleted"', [id]);
            return row ? new MovieLink(MovieLink.parseRow(row)) : null;
        } catch (error) {
            logger.error('Erro ao buscar link por ID:', { error: error.message, id });
            throw error;
        }
    }

    /**
     * Busca links por filme
     */
    static async findByMovieId(movieId, options = {}) {
        try {
            const { resolution = '', provider = '', status = 'active' } = options;
            
            let whereConditions = ['movie_id = ?', 'status = ?'];
            let params = [movieId, status];

            if (resolution) {
                whereConditions.push('resolution = ?');
                params.push(resolution);
            }

            if (provider) {
                whereConditions.push('provider = ?');
                params.push(provider);
            }

            const whereClause = whereConditions.join(' AND ');
            const rows = await database.all(`
                SELECT * FROM movie_links 
                WHERE ${whereClause}
                ORDER BY resolution DESC, created_at DESC
            `, params);
            
            return rows.map(row => new MovieLink(MovieLink.parseRow(row)));
        } catch (error) {
            logger.error('Erro ao buscar links por filme:', { error: error.message, movieId });
            throw error;
        }
    }

    /**
     * Lista todos os links com filtros
     */
    static async findAll(options = {}) {
        try {
            const {
                page = 1,
                limit = 20,
                resolution = '',
                provider = '',
                status = 'active',
                verified = null,
                sortBy = 'created_at',
                sortOrder = 'DESC'
            } = options;

            const offset = (page - 1) * limit;
            let whereConditions = ['status = ?'];
            let params = [status];

            if (resolution) {
                whereConditions.push('resolution = ?');
                params.push(resolution);
            }

            if (provider) {
                whereConditions.push('provider = ?');
                params.push(provider);
            }

            if (verified !== null) {
                whereConditions.push('is_verified = ?');
                params.push(verified ? 1 : 0);
            }

            const whereClause = whereConditions.join(' AND ');
            
            // Contar total
            const countResult = await database.get(`
                SELECT COUNT(*) as total FROM movie_links WHERE ${whereClause}
            `, params);
            const total = countResult.total;

            // Buscar links
            const rows = await database.all(`
                SELECT * FROM movie_links 
                WHERE ${whereClause}
                ORDER BY ${sortBy} ${sortOrder}
                LIMIT ? OFFSET ?
            `, [...params, limit, offset]);

            const links = rows.map(row => new MovieLink(MovieLink.parseRow(row)));

            return {
                links,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            logger.error('Erro ao listar links:', { error: error.message, options });
            throw error;
        }
    }

    /**
     * Busca links que precisam de verificação
     */
    static async findUnverified(limit = 50) {
        try {
            const rows = await database.all(`
                SELECT * FROM movie_links 
                WHERE status = "active" 
                AND (is_verified = 0 OR last_verified IS NULL OR last_verified < datetime('now', '-7 days'))
                ORDER BY created_at ASC
                LIMIT ?
            `, [limit]);
            
            return rows.map(row => new MovieLink(MovieLink.parseRow(row)));
        } catch (error) {
            logger.error('Erro ao buscar links não verificados:', { error: error.message });
            throw error;
        }
    }

    /**
     * Atualiza status de verificação
     */
    async updateVerificationStatus(isValid, error = '') {
        try {
            this.is_verified = isValid;
            this.last_verified = new Date().toISOString();
            this.verification_status = isValid ? 'valid' : 'invalid';
            this.verification_error = error;
            this.updated_at = new Date().toISOString();

            await database.run(`
                UPDATE movie_links SET
                    is_verified = ?, last_verified = ?, verification_status = ?,
                    verification_error = ?, updated_at = ?
                WHERE id = ?
            `, [
                this.is_verified, this.last_verified, this.verification_status,
                this.verification_error, this.updated_at, this.id
            ]);

            logger.info(`Verificação atualizada: ${this.url} - ${isValid ? 'Válido' : 'Inválido'}`);
            return this;
        } catch (error) {
            logger.error('Erro ao atualizar verificação:', { error: error.message, id: this.id });
            throw error;
        }
    }

    /**
     * Incrementa contador de download
     */
    async incrementDownloadCount() {
        try {
            this.download_count++;
            this.updated_at = new Date().toISOString();

            await database.run(`
                UPDATE movie_links SET download_count = ?, updated_at = ? WHERE id = ?
            `, [this.download_count, this.updated_at, this.id]);

            return this;
        } catch (error) {
            logger.error('Erro ao incrementar download:', { error: error.message, id: this.id });
            throw error;
        }
    }

    /**
     * Incrementa contador de stream
     */
    async incrementStreamCount() {
        try {
            this.stream_count++;
            this.updated_at = new Date().toISOString();

            await database.run(`
                UPDATE movie_links SET stream_count = ?, updated_at = ? WHERE id = ?
            `, [this.stream_count, this.updated_at, this.id]);

            return this;
        } catch (error) {
            logger.error('Erro ao incrementar stream:', { error: error.message, id: this.id });
            throw error;
        }
    }

    /**
     * Remove link (soft delete)
     */
    async delete() {
        try {
            this.status = 'deleted';
            this.updated_at = new Date().toISOString();
            
            await database.run('UPDATE movie_links SET status = ?, updated_at = ? WHERE id = ?', 
                [this.status, this.updated_at, this.id]);
            
            logger.info(`Link removido: ${this.url} (ID: ${this.id})`);
            return true;
        } catch (error) {
            logger.error('Erro ao remover link:', { error: error.message, id: this.id });
            throw error;
        }
    }

    /**
     * Obtém estatísticas de links por resolução
     */
    static async getResolutionStats() {
        try {
            const rows = await database.all(`
                SELECT resolution, COUNT(*) as count
                FROM movie_links 
                WHERE status = "active"
                GROUP BY resolution
                ORDER BY count DESC
            `);
            
            return rows;
        } catch (error) {
            logger.error('Erro ao obter estatísticas de resolução:', { error: error.message });
            throw error;
        }
    }

    /**
     * Obtém estatísticas de links por provedor
     */
    static async getProviderStats() {
        try {
            const rows = await database.all(`
                SELECT provider, COUNT(*) as count
                FROM movie_links 
                WHERE status = "active"
                GROUP BY provider
                ORDER BY count DESC
            `);
            
            return rows;
        } catch (error) {
            logger.error('Erro ao obter estatísticas de provedor:', { error: error.message });
            throw error;
        }
    }

    /**
     * Converte dados do banco para objeto
     */
    static parseRow(row) {
        return {
            ...row,
            is_public: Boolean(row.is_public),
            is_verified: Boolean(row.is_verified)
        };
    }

    /**
     * Converte para JSON
     */
    toJSON() {
        return {
            id: this.id,
            movie_id: this.movie_id,
            url: this.url,
            provider: this.provider,
            resolution: this.resolution,
            quality: this.quality,
            size_mb: this.size_mb,
            language: this.language,
            subtitle_language: this.subtitle_language,
            audio_format: this.audio_format,
            video_format: this.video_format,
            is_public: this.is_public,
            is_verified: this.is_verified,
            last_verified: this.last_verified,
            verification_status: this.verification_status,
            verification_error: this.verification_error,
            download_count: this.download_count,
            stream_count: this.stream_count,
            user_notes: this.user_notes,
            status: this.status,
            created_at: this.created_at,
            updated_at: this.updated_at
        };
    }
}

module.exports = MovieLink;
