/**
 * Serviço de Detecção de Qualidade de Vídeo
 */

const database = require('../config/database');
const logger = require('../utils/logger');
const crypto = require('crypto');

class QualityDetectionService {
    constructor() {
        this.qualityPatterns = {
            '4K': [
                /2160p/i, /4k/i, /uhd/i, /ultra.*hd/i,
                /3840x2160/i, /4096x2160/i
            ],
            '1080p': [
                /1080p/i, /1920x1080/i, /full.*hd/i, /fhd/i,
                /bluray.*1080/i, /bd.*1080/i
            ],
            '720p': [
                /720p/i, /1280x720/i, /hd/i, /hdtv/i,
                /bluray.*720/i, /bd.*720/i
            ],
            '480p': [
                /480p/i, /854x480/i, /640x480/i, /dvd/i,
                /sd/i, /standard/i
            ],
            '360p': [
                /360p/i, /640x360/i, /mobile/i
            ],
            '240p': [
                /240p/i, /320x240/i, /low/i
            ]
        };

        this.codecPatterns = {
            'H.264': [/h\.?264/i, /avc/i, /x264/i],
            'H.265': [/h\.?265/i, /hevc/i, /x265/i],
            'VP9': [/vp9/i],
            'AV1': [/av1/i],
            'XVID': [/xvid/i],
            'DIVX': [/divx/i]
        };

        this.sourcePatterns = {
            'BluRay': [/bluray/i, /bd/i, /bdrip/i, /bdremux/i],
            'WEB-DL': [/web.*dl/i, /webdl/i],
            'WEBRip': [/web.*rip/i, /webrip/i],
            'DVDRip': [/dvd.*rip/i, /dvdrip/i],
            'HDTV': [/hdtv/i, /hdtvrip/i],
            'CAM': [/cam/i, /camrip/i],
            'TS': [/ts/i, /telesync/i],
            'TC': [/tc/i, /telecine/i],
            'SCR': [/scr/i, /screener/i]
        };
    }

    /**
     * Detecta qualidade de um torrent
     */
    async detectQuality(torrentTitle, magnetLink = null) {
        try {
            const magnetHash = magnetLink ? this.extractHashFromMagnet(magnetLink) : null;
            
            // Verificar cache primeiro
            if (magnetHash) {
                const cached = await this.getCachedQuality(magnetHash);
                if (cached) {
                    logger.debug(`Qualidade encontrada no cache: ${cached.detected_quality}`);
                    return cached;
                }
            }

            // Detectar qualidade
            const detection = this.analyzeTitle(torrentTitle);
            
            // Salvar no cache se temos hash
            if (magnetHash && detection.confidence > 0.5) {
                await this.cacheQuality(magnetHash, detection);
            }

            return detection;
        } catch (error) {
            logger.error('Erro na detecção de qualidade:', error.message);
            return this.getDefaultQuality();
        }
    }

    /**
     * Analisa título do torrent
     */
    analyzeTitle(title) {
        const analysis = {
            detected_quality: 'Unknown',
            resolution: 'Unknown',
            codec: 'Unknown',
            source: 'Unknown',
            confidence_score: 0,
            detection_method: 'title_analysis',
            details: {}
        };

        let confidence = 0;
        const titleLower = title.toLowerCase();

        // Detectar resolução/qualidade
        for (const [quality, patterns] of Object.entries(this.qualityPatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(title)) {
                    analysis.detected_quality = quality;
                    analysis.resolution = quality;
                    confidence += 0.8;
                    analysis.details.quality_match = pattern.source;
                    break;
                }
            }
            if (analysis.detected_quality !== 'Unknown') break;
        }

        // Detectar codec
        for (const [codec, patterns] of Object.entries(this.codecPatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(title)) {
                    analysis.codec = codec;
                    confidence += 0.1;
                    analysis.details.codec_match = pattern.source;
                    break;
                }
            }
            if (analysis.codec !== 'Unknown') break;
        }

        // Detectar fonte
        for (const [source, patterns] of Object.entries(this.sourcePatterns)) {
            for (const pattern of patterns) {
                if (pattern.test(title)) {
                    analysis.source = source;
                    confidence += 0.1;
                    analysis.details.source_match = pattern.source;
                    break;
                }
            }
            if (analysis.source !== 'Unknown') break;
        }

        // Ajustar confiança baseado em padrões adicionais
        confidence += this.calculateAdditionalConfidence(title, analysis);
        
        analysis.confidence_score = Math.min(confidence, 1.0);

        // Se não detectou qualidade, tentar inferir
        if (analysis.detected_quality === 'Unknown') {
            analysis.detected_quality = this.inferQualityFromContext(title, analysis);
            analysis.resolution = analysis.detected_quality;
            analysis.confidence_score = Math.max(analysis.confidence_score, 0.3);
        }

        logger.debug(`Qualidade detectada: ${analysis.detected_quality} (confiança: ${analysis.confidence_score})`);
        return analysis;
    }

    /**
     * Calcula confiança adicional
     */
    calculateAdditionalConfidence(title, analysis) {
        let bonus = 0;
        const titleLower = title.toLowerCase();

        // Bonus por tamanho de arquivo mencionado
        if (/\d+(\.\d+)?\s*(gb|mb)/i.test(title)) {
            bonus += 0.1;
        }

        // Bonus por grupo de release conhecido
        if (/\[(.*?)\]|\-(.*?)$/i.test(title)) {
            bonus += 0.05;
        }

        // Bonus por ano mencionado
        if (/\b(19|20)\d{2}\b/.test(title)) {
            bonus += 0.05;
        }

        // Penalidade por títulos muito curtos ou suspeitos
        if (title.length < 20) {
            bonus -= 0.2;
        }

        return bonus;
    }

    /**
     * Infere qualidade do contexto
     */
    inferQualityFromContext(title, analysis) {
        const titleLower = title.toLowerCase();

        // Inferir por fonte
        if (analysis.source === 'BluRay') {
            return '1080p'; // BluRay geralmente é 1080p ou superior
        } else if (analysis.source === 'DVDRip') {
            return '480p'; // DVD é tipicamente 480p
        } else if (analysis.source === 'HDTV') {
            return '720p'; // HDTV geralmente é 720p
        } else if (analysis.source === 'CAM' || analysis.source === 'TS') {
            return '480p'; // Qualidade baixa
        }

        // Inferir por codec
        if (analysis.codec === 'H.265' || analysis.codec === 'AV1') {
            return '1080p'; // Codecs modernos sugerem qualidade alta
        }

        // Padrão conservador
        return '720p';
    }

    /**
     * Extrai hash do magnet link
     */
    extractHashFromMagnet(magnetLink) {
        try {
            const match = magnetLink.match(/xt=urn:btih:([a-fA-F0-9]{40})/);
            return match ? match[1].toLowerCase() : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Busca qualidade no cache
     */
    async getCachedQuality(magnetHash) {
        try {
            const sql = 'SELECT * FROM video_quality_cache WHERE magnet_hash = ?';
            const row = await database.get(sql, [magnetHash]);
            
            if (row) {
                return {
                    detected_quality: row.detected_quality,
                    resolution: row.resolution,
                    codec: row.codec,
                    confidence_score: row.confidence_score,
                    detection_method: row.detection_method,
                    cached: true
                };
            }
            
            return null;
        } catch (error) {
            logger.error('Erro ao buscar cache de qualidade:', error.message);
            return null;
        }
    }

    /**
     * Salva qualidade no cache
     */
    async cacheQuality(magnetHash, detection) {
        try {
            const sql = `
                INSERT OR REPLACE INTO video_quality_cache (
                    magnet_hash, detected_quality, resolution, codec,
                    confidence_score, detection_method, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;
            
            const now = new Date().toISOString();
            
            await database.run(sql, [
                magnetHash,
                detection.detected_quality,
                detection.resolution,
                detection.codec,
                detection.confidence_score,
                detection.detection_method,
                now,
                now
            ]);
            
            logger.debug(`Qualidade salva no cache: ${magnetHash} -> ${detection.detected_quality}`);
        } catch (error) {
            logger.error('Erro ao salvar cache de qualidade:', error.message);
        }
    }

    /**
     * Qualidade padrão quando detecção falha
     */
    getDefaultQuality() {
        return {
            detected_quality: '720p',
            resolution: '720p',
            codec: 'Unknown',
            source: 'Unknown',
            confidence_score: 0.1,
            detection_method: 'default',
            details: {}
        };
    }

    /**
     * Limpa cache antigo
     */
    async cleanupCache(daysOld = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            
            const sql = 'DELETE FROM video_quality_cache WHERE created_at < ?';
            const result = await database.run(sql, [cutoffDate.toISOString()]);
            
            logger.info(`Cache de qualidade limpo: ${result.changes} entradas removidas`);
            return result.changes;
        } catch (error) {
            logger.error('Erro na limpeza do cache:', error.message);
            throw error;
        }
    }

    /**
     * Estatísticas do cache
     */
    async getCacheStats() {
        try {
            const [total, byQuality] = await Promise.all([
                database.get('SELECT COUNT(*) as count FROM video_quality_cache'),
                database.all(`
                    SELECT detected_quality, COUNT(*) as count 
                    FROM video_quality_cache 
                    GROUP BY detected_quality 
                    ORDER BY count DESC
                `)
            ]);

            return {
                totalEntries: total.count,
                qualityDistribution: byQuality
            };
        } catch (error) {
            logger.error('Erro ao obter estatísticas do cache:', error.message);
            return { totalEntries: 0, qualityDistribution: [] };
        }
    }
}

// Singleton
const qualityDetectionService = new QualityDetectionService();

module.exports = qualityDetectionService;
