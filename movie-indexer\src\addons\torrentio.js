const axios = require('axios');
const logger = require('../utils/logger');

/**
 * <PERSON>don Torrentio
 * Conecta com o addon Torrentio para buscar torrents
 */
class TorrentioAddon {
    constructor() {
        this.baseUrl = 'https://torrentio.strem.fun';
        this.name = 'Torrentio';
        this.timeout = 10000;
    }

    /**
     * Busca torrents no Torrentio
     */
    async search(query, options = {}) {
        try {
            const { imdbId, tmdbId, season, episode } = options;
            
            // Se temos IMDB ID, usar busca direta
            if (imdbId) {
                return await this.searchByImdbId(imdbId, season, episode);
            }
            
            // <PERSON><PERSON>o contrário, buscar por título
            return await this.searchByTitle(query, options);
            
        } catch (error) {
            logger.error('Erro no Torrentio addon:', error.message);
            return [];
        }
    }

    /**
     * Busca por IMDB ID (mais preciso)
     */
    async searchByImdbId(imdbId, season = null, episode = null) {
        try {
            let streamType = 'movie';
            let streamId = imdbId;

            if (season && episode) {
                streamType = 'series';
                streamId = `${imdbId}:${season}:${episode}`;
            }

            const url = `${this.baseUrl}/stream/${streamType}/${streamId}.json`;
            
            const response = await axios.get(url, {
                timeout: this.timeout,
                headers: {
                    'User-Agent': 'MovieStash/1.0'
                }
            });

            if (response.data && response.data.streams) {
                return this.parseTorrentioStreams(response.data.streams);
            }

            return [];
        } catch (error) {
            logger.warn(`Erro na busca Torrentio por IMDB ${imdbId}:`, error.message);
            return [];
        }
    }

    /**
     * Busca por título (menos preciso)
     */
    async searchByTitle(title, options = {}) {
        try {
            // Torrentio não tem busca por título direta
            // Vamos tentar usar a API de catálogo se disponível
            logger.info(`Busca por título no Torrentio: ${title}`);
            
            // Por enquanto retorna vazio, pois Torrentio funciona melhor com IMDB ID
            return [];
        } catch (error) {
            logger.warn(`Erro na busca Torrentio por título "${title}":`, error.message);
            return [];
        }
    }

    /**
     * Converte streams do Torrentio para formato padrão
     */
    parseTorrentioStreams(streams) {
        const torrents = [];

        streams.forEach(stream => {
            try {
                const torrent = this.parseStream(stream);
                if (torrent) {
                    torrents.push(torrent);
                }
            } catch (error) {
                logger.warn('Erro ao processar stream do Torrentio:', error.message);
            }
        });

        return torrents;
    }

    /**
     * Processa um stream individual
     */
    parseStream(stream) {
        if (!stream.url || !stream.title) {
            return null;
        }

        // Extrair informações do título
        const title = stream.title;
        const quality = this.extractQuality(title);
        const size = this.extractSize(title);
        const seeders = this.extractSeeders(title);

        // Converter URL do Torrentio para magnet link
        const magnetLink = this.extractMagnetFromUrl(stream.url);

        return {
            title: title,
            magnet: magnetLink,
            quality: quality,
            size: size,
            seeders: seeders,
            provider: 'Torrentio',
            source: stream.url,
            hash: this.extractHashFromMagnet(magnetLink)
        };
    }

    /**
     * Extrai qualidade do título
     */
    extractQuality(title) {
        const titleLower = title.toLowerCase();
        
        if (titleLower.includes('4k') || titleLower.includes('2160p')) {
            return '4K';
        } else if (titleLower.includes('1080p')) {
            return '1080p';
        } else if (titleLower.includes('720p')) {
            return '720p';
        } else if (titleLower.includes('480p')) {
            return '480p';
        }
        
        return 'Unknown';
    }

    /**
     * Extrai tamanho do arquivo
     */
    extractSize(title) {
        const sizeMatch = title.match(/(\d+(?:\.\d+)?)\s*(GB|MB|TB)/i);
        if (sizeMatch) {
            return `${sizeMatch[1]} ${sizeMatch[2].toUpperCase()}`;
        }
        return 'Unknown';
    }

    /**
     * Extrai número de seeders
     */
    extractSeeders(title) {
        const seedersMatch = title.match(/👤\s*(\d+)/);
        if (seedersMatch) {
            return parseInt(seedersMatch[1]);
        }
        return 0;
    }

    /**
     * Extrai magnet link da URL do Torrentio
     */
    extractMagnetFromUrl(url) {
        try {
            // URLs do Torrentio geralmente contêm o hash
            const hashMatch = url.match(/([a-fA-F0-9]{40})/);
            if (hashMatch) {
                const hash = hashMatch[1];
                return `magnet:?xt=urn:btih:${hash}`;
            }
            
            // Se a URL já é um magnet link
            if (url.startsWith('magnet:')) {
                return url;
            }
            
            return url;
        } catch (error) {
            logger.warn('Erro ao extrair magnet da URL:', error.message);
            return url;
        }
    }

    /**
     * Extrai hash do magnet link
     */
    extractHashFromMagnet(magnetLink) {
        try {
            const hashMatch = magnetLink.match(/xt=urn:btih:([a-fA-F0-9]{40})/);
            if (hashMatch) {
                return hashMatch[1];
            }
            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Testa conectividade com o Torrentio
     */
    async testConnection() {
        try {
            const response = await axios.get(`${this.baseUrl}/manifest.json`, {
                timeout: 5000
            });
            
            return {
                success: true,
                name: response.data.name || 'Torrentio',
                version: response.data.version || 'Unknown'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = TorrentioAddon;
