const axios = require('axios');
const logger = require('../utils/logger');

/**
 * <PERSON>don Torrentio
 * Conecta com o addon Torrentio para buscar torrents
 */
class TorrentioAddon {
    constructor() {
        this.baseUrl = 'https://torrentio.strem.fun';
        this.name = 'Torrentio';
        this.timeout = 10000;
    }

    /**
     * Busca torrents no Torrentio
     */
    async search(query, options = {}) {
        try {
            const { imdbId, tmdbId, season, episode, year } = options;

            logger.info(`Torrentio search iniciada: query="${query}", imdbId="${imdbId}", tmdbId="${tmdbId}", year="${year}"`);

            // Se temos IMDB ID, usar busca direta
            if (imdbId) {
                logger.info(`Torrentio: Usando busca por IMDB ID: ${imdbId}`);
                const results = await this.searchByImdbId(imdbId, season, episode);
                logger.info(`Torrentio: Encontrados ${results.length} resultados por IMDB ID`);
                return results;
            }

            // Caso contrário, buscar por título
            logger.info(`Torrentio: Usando busca por título: ${query}`);
            const results = await this.searchByTitle(query, options);
            logger.info(`Torrentio: Encontrados ${results.length} resultados por título`);
            return results;

        } catch (error) {
            logger.error('Erro no Torrentio addon:', error.message);
            return [];
        }
    }

    /**
     * Busca por IMDB ID (mais preciso)
     */
    async searchByImdbId(imdbId, season = null, episode = null) {
        try {
            let streamType = 'movie';
            let streamId = imdbId;

            if (season && episode) {
                streamType = 'series';
                streamId = `${imdbId}:${season}:${episode}`;
            }

            const url = `${this.baseUrl}/stream/${streamType}/${streamId}.json`;
            logger.info(`Torrentio: Fazendo requisição para: ${url}`);

            const response = await axios.get(url, {
                timeout: this.timeout,
                headers: {
                    'User-Agent': 'MovieStash/1.0'
                }
            });

            logger.info(`Torrentio: Status da resposta: ${response.status}`);
            logger.info(`Torrentio: Dados recebidos:`, response.data ? 'Sim' : 'Não');

            if (response.data && response.data.streams) {
                logger.info(`Torrentio: Encontrados ${response.data.streams.length} streams brutos`);
                const parsed = this.parseTorrentioStreams(response.data.streams);
                logger.info(`Torrentio: Processados ${parsed.length} torrents`);
                return parsed;
            }

            logger.warn(`Torrentio: Nenhum stream encontrado para ${imdbId}`);
            return [];
        } catch (error) {
            logger.warn(`Erro na busca Torrentio por IMDB ${imdbId}:`, error.message);
            if (error.response) {
                logger.warn(`Status HTTP: ${error.response.status}`);
                logger.warn(`Dados da resposta:`, error.response.data);
            }
            return [];
        }
    }

    /**
     * Busca por título (menos preciso)
     */
    async searchByTitle(title, options = {}) {
        try {
            const { year, tmdbId } = options;

            // Se temos TMDb ID, tentar buscar IMDB ID primeiro
            if (tmdbId) {
                const imdbId = await this.getImdbIdFromTmdb(tmdbId);
                if (imdbId) {
                    logger.info(`Torrentio: IMDB ID encontrado via TMDb: ${imdbId}`);
                    return await this.searchByImdbId(imdbId);
                }
            }

            // Tentar busca alternativa
            logger.info(`Torrentio: Tentando busca alternativa para: ${title} (${year})`);
            return await this.alternativeSearch(title, year);

        } catch (error) {
            logger.warn(`Erro na busca Torrentio por título "${title}":`, error.message);
            return [];
        }
    }

    /**
     * Busca IMDB ID via TMDb
     */
    async getImdbIdFromTmdb(tmdbId) {
        try {
            const tmdbService = require('../services/tmdbService');
            const movieDetails = await tmdbService.getMovieDetails(tmdbId);
            return movieDetails.imdb_id;
        } catch (error) {
            logger.warn(`Erro ao buscar IMDB ID para TMDb ${tmdbId}:`, error.message);
            return null;
        }
    }

    /**
     * Busca alternativa usando diferentes estratégias
     */
    async alternativeSearch(title, year) {
        try {
            // Estratégia: usar endpoint de catálogo
            const url = `${this.baseUrl}/catalog/movie/top/search=${encodeURIComponent(title)}.json`;

            const response = await axios.get(url, {
                timeout: this.timeout,
                headers: {
                    'User-Agent': 'MovieStash/1.0'
                }
            });

            if (response.data && response.data.metas) {
                // Converter metas para streams
                const streams = [];
                for (const meta of response.data.metas.slice(0, 3)) { // Limitar a 3 resultados
                    if (meta.imdb_id) {
                        logger.info(`Encontrado IMDB ID via busca: ${meta.imdb_id} para ${meta.name}`);
                        const metaStreams = await this.searchByImdbId(meta.imdb_id);
                        streams.push(...metaStreams);
                    }
                }
                return streams;
            }

            return [];

        } catch (error) {
            logger.debug('Busca alternativa falhou:', error.message);
            return [];
        }
    }

    /**
     * Converte streams do Torrentio para formato padrão
     */
    parseTorrentioStreams(streams) {
        const torrents = [];
        logger.info(`Iniciando parse de ${streams.length} streams`);
        logger.info(`Primeiro stream:`, streams[0]);

        streams.forEach((stream, index) => {
            try {
                const torrent = this.parseStream(stream);
                if (torrent) {
                    torrents.push(torrent);
                }
            } catch (error) {
                logger.warn('Erro ao processar stream do Torrentio:', error.message);
            }
        });

        return torrents;
    }

    /**
     * Processa um stream individual
     */
    parseStream(stream) {
        logger.info(`Processando stream:`, { url: stream.url, title: stream.title });

        if (!stream.url || !stream.title) {
            logger.info(`Stream rejeitado: URL ou título ausente`);
            return null;
        }

        // Extrair informações do título
        const title = stream.title;
        const quality = this.extractQuality(title);
        const size = this.extractSize(title);
        const seeders = this.extractSeeders(title);

        // Converter URL do Torrentio para magnet link
        const magnetLink = this.extractMagnetFromUrl(stream.url);

        if (!magnetLink) {
            logger.debug(`Stream rejeitado: Não foi possível extrair magnet link de ${stream.url}`);
            return null;
        }

        const result = {
            title: title,
            magnet: magnetLink,
            quality: quality,
            size: size,
            seeders: seeders,
            provider: 'Torrentio',
            source: stream.url,
            hash: this.extractHashFromMagnet(magnetLink)
        };

        logger.debug(`Stream processado com sucesso:`, result);
        return result;
    }

    /**
     * Extrai qualidade do título
     */
    extractQuality(title) {
        const titleLower = title.toLowerCase();
        
        if (titleLower.includes('4k') || titleLower.includes('2160p')) {
            return '4K';
        } else if (titleLower.includes('1080p')) {
            return '1080p';
        } else if (titleLower.includes('720p')) {
            return '720p';
        } else if (titleLower.includes('480p')) {
            return '480p';
        }
        
        return 'Unknown';
    }

    /**
     * Extrai tamanho do arquivo
     */
    extractSize(title) {
        const sizeMatch = title.match(/(\d+(?:\.\d+)?)\s*(GB|MB|TB)/i);
        if (sizeMatch) {
            return `${sizeMatch[1]} ${sizeMatch[2].toUpperCase()}`;
        }
        return 'Unknown';
    }

    /**
     * Extrai número de seeders
     */
    extractSeeders(title) {
        const seedersMatch = title.match(/👤\s*(\d+)/);
        if (seedersMatch) {
            return parseInt(seedersMatch[1]);
        }
        return 0;
    }

    /**
     * Extrai magnet link da URL do Torrentio
     */
    extractMagnetFromUrl(url) {
        try {
            // URLs do Torrentio geralmente contêm o hash
            const hashMatch = url.match(/([a-fA-F0-9]{40})/);
            if (hashMatch) {
                const hash = hashMatch[1];
                return `magnet:?xt=urn:btih:${hash}`;
            }
            
            // Se a URL já é um magnet link
            if (url.startsWith('magnet:')) {
                return url;
            }
            
            return url;
        } catch (error) {
            logger.warn('Erro ao extrair magnet da URL:', error.message);
            return url;
        }
    }

    /**
     * Extrai hash do magnet link
     */
    extractHashFromMagnet(magnetLink) {
        try {
            const hashMatch = magnetLink.match(/xt=urn:btih:([a-fA-F0-9]{40})/);
            if (hashMatch) {
                return hashMatch[1];
            }
            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Testa conectividade com o Torrentio
     */
    async testConnection() {
        try {
            const response = await axios.get(`${this.baseUrl}/manifest.json`, {
                timeout: 5000
            });
            
            return {
                success: true,
                name: response.data.name || 'Torrentio',
                version: response.data.version || 'Unknown'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = TorrentioAddon;
