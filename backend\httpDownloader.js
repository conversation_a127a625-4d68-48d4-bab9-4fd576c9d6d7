const fs = require('fs');
const path = require('path');
const axios = require('axios');
const ProgressBar = require('progress');

/**
 * Classe para fazer download de arquivos via HTTP/HTTPS
 */
class HttpDownloader {
    constructor() {
        this.downloadsPath = path.join(__dirname, '..', 'downloads');
        this.ensureDownloadsDirectory();
    }

    /**
     * Garante que a pasta de downloads existe
     */
    ensureDownloadsDirectory() {
        if (!fs.existsSync(this.downloadsPath)) {
            fs.mkdirSync(this.downloadsPath, { recursive: true });
            console.log(`📁 Pasta de downloads criada: ${this.downloadsPath}`);
        }
    }

    /**
     * Extrai o nome do arquivo da URL
     * @param {string} url - URL do arquivo
     * @returns {string} Nome do arquivo
     */
    getFileNameFromUrl(url) {
        try {
            const urlObj = new URL(url);
            let fileName = path.basename(urlObj.pathname);
            
            // Se não conseguir extrair nome, gera um nome baseado no timestamp
            if (!fileName || fileName === '/') {
                const timestamp = Date.now();
                fileName = `download_${timestamp}`;
            }
            
            return fileName;
        } catch (error) {
            const timestamp = Date.now();
            return `download_${timestamp}`;
        }
    }

    /**
     * Converte bytes para formato legível
     * @param {number} bytes - Número de bytes
     * @returns {string} Tamanho formatado
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Faz download de um arquivo via HTTP/HTTPS
     * @param {string} url - URL do arquivo para download
     * @param {string} customFileName - Nome personalizado para o arquivo (opcional)
     * @returns {Promise<Object>} Resultado do download
     */
    async downloadFile(url, customFileName = null) {
        try {
            console.log(`🚀 Iniciando download: ${url}`);
            
            // Primeiro, faz uma requisição HEAD para obter informações do arquivo
            const headResponse = await axios.head(url);
            const contentLength = parseInt(headResponse.headers['content-length'] || '0');
            
            console.log(`📊 Tamanho do arquivo: ${this.formatBytes(contentLength)}`);
            
            // Define o nome do arquivo
            const fileName = customFileName || this.getFileNameFromUrl(url);
            const filePath = path.join(this.downloadsPath, fileName);
            
            // Verifica se o arquivo já existe
            if (fs.existsSync(filePath)) {
                const timestamp = Date.now();
                const ext = path.extname(fileName);
                const name = path.basename(fileName, ext);
                const newFileName = `${name}_${timestamp}${ext}`;
                const newFilePath = path.join(this.downloadsPath, newFileName);
                console.log(`⚠️  Arquivo já existe, salvando como: ${newFileName}`);
                return this.downloadFile(url, newFileName);
            }

            // Cria stream de escrita
            const writer = fs.createWriteStream(filePath);
            
            // Configura barra de progresso
            let progressBar = null;
            if (contentLength > 0) {
                progressBar = new ProgressBar('⬇️  Baixando [:bar] :percent :rate/bps :etas', {
                    complete: '█',
                    incomplete: '░',
                    width: 40,
                    total: contentLength
                });
            }

            // Faz o download com stream
            const response = await axios({
                method: 'GET',
                url: url,
                responseType: 'stream',
                timeout: 30000, // 30 segundos de timeout
                headers: {
                    'User-Agent': 'Movie-Stash-Downloader/1.0'
                }
            });

            let downloadedBytes = 0;

            // Monitora o progresso
            response.data.on('data', (chunk) => {
                downloadedBytes += chunk.length;
                if (progressBar) {
                    progressBar.tick(chunk.length);
                } else {
                    // Se não tiver content-length, mostra progresso simples
                    process.stdout.write(`\r⬇️  Baixado: ${this.formatBytes(downloadedBytes)}`);
                }
            });

            // Pipe do response para o arquivo
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    console.log(`\n✅ Download concluído!`);
                    console.log(`📁 Arquivo salvo em: ${filePath}`);
                    console.log(`📊 Tamanho final: ${this.formatBytes(downloadedBytes)}`);
                    
                    resolve({
                        success: true,
                        filePath: filePath,
                        fileName: fileName,
                        size: downloadedBytes,
                        sizeFormatted: this.formatBytes(downloadedBytes)
                    });
                });

                writer.on('error', (error) => {
                    console.error(`❌ Erro ao escrever arquivo: ${error.message}`);
                    // Remove arquivo parcial em caso de erro
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                    }
                    reject(error);
                });

                response.data.on('error', (error) => {
                    console.error(`❌ Erro no download: ${error.message}`);
                    writer.destroy();
                    // Remove arquivo parcial em caso de erro
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                    }
                    reject(error);
                });
            });

        } catch (error) {
            console.error(`❌ Erro no download: ${error.message}`);
            throw {
                success: false,
                error: error.message,
                url: url
            };
        }
    }

    /**
     * Lista arquivos na pasta de downloads
     * @returns {Array} Lista de arquivos baixados
     */
    listDownloadedFiles() {
        try {
            const files = fs.readdirSync(this.downloadsPath);
            return files.map(file => {
                const filePath = path.join(this.downloadsPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    path: filePath,
                    size: stats.size,
                    sizeFormatted: this.formatBytes(stats.size),
                    created: stats.birthtime,
                    modified: stats.mtime
                };
            });
        } catch (error) {
            console.error(`❌ Erro ao listar arquivos: ${error.message}`);
            return [];
        }
    }
}

module.exports = HttpDownloader;
