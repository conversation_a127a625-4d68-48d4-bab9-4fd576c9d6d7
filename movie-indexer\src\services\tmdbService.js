/**
 * Serviço de Integração com API do TMDb
 */

const axios = require('axios');
const logger = require('../utils/logger');

class TMDbService {
    constructor() {
        this.apiKey = process.env.TMDB_API_KEY;
        this.baseUrl = process.env.TMDB_BASE_URL || 'https://api.themoviedb.org/3';
        this.imageBaseUrl = process.env.TMDB_IMAGE_BASE_URL || 'https://image.tmdb.org/t/p';
        this.language = 'pt-BR';
        this.timeout = 10000;
        this.cache = new Map();
        this.cacheTimeout = 3600000; // 1 hora
    }

    /**
     * Faz requisição para a API do TMDb
     */
    async makeRequest(endpoint, params = {}) {
        try {
            const url = `${this.baseUrl}${endpoint}`;
            const config = {
                params: {
                    api_key: this.apiKey,
                    language: this.language,
                    ...params
                },
                timeout: this.timeout
            };

            logger.debug(`TMDb Request: ${endpoint}`, params);
            
            const response = await axios.get(url, config);
            return response.data;
        } catch (error) {
            logger.error('Erro na requisição TMDb:', { 
                error: error.message, 
                endpoint,
                status: error.response?.status 
            });
            throw new Error(`Erro TMDb: ${error.message}`);
        }
    }

    /**
     * Busca filmes por título
     */
    async searchMovies(query, page = 1) {
        try {
            const cacheKey = `search_${query}_${page}`;
            
            // Verificar cache
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    logger.debug('Retornando resultado do cache:', cacheKey);
                    return cached.data;
                }
            }

            const data = await this.makeRequest('/search/movie', {
                query: query,
                page: page,
                include_adult: false
            });

            const formattedResults = {
                results: data.results.map(movie => this.formatMovieBasic(movie)),
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };

            // Salvar no cache
            this.cache.set(cacheKey, {
                data: formattedResults,
                timestamp: Date.now()
            });

            logger.info(`Busca TMDb: "${query}" - ${data.results.length} resultados`);
            return formattedResults;
        } catch (error) {
            logger.error('Erro ao buscar filmes:', { error: error.message, query });
            throw error;
        }
    }

    /**
     * Obtém detalhes completos de um filme
     */
    async getMovieDetails(movieId) {
        try {
            const cacheKey = `movie_${movieId}`;
            
            // Verificar cache
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    logger.debug('Retornando detalhes do cache:', cacheKey);
                    return cached.data;
                }
            }

            const movie = await this.makeRequest(`/movie/${movieId}`, {
                append_to_response: 'credits,videos,images,keywords,similar,recommendations,external_ids'
            });

            const formattedMovie = this.formatMovieDetailed(movie);

            // Salvar no cache
            this.cache.set(cacheKey, {
                data: formattedMovie,
                timestamp: Date.now()
            });

            logger.info(`Detalhes TMDb obtidos: ${movie.title} (${movie.id})`);
            return formattedMovie;
        } catch (error) {
            logger.error('Erro ao obter detalhes do filme:', { error: error.message, movieId });
            throw error;
        }
    }

    /**
     * Obtém filmes populares
     */
    async getPopularMovies(page = 1) {
        try {
            const data = await this.makeRequest('/movie/popular', { page });
            
            return {
                results: data.results.map(movie => this.formatMovieBasic(movie)),
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };
        } catch (error) {
            logger.error('Erro ao obter filmes populares:', { error: error.message });
            throw error;
        }
    }

    /**
     * Obtém filmes em cartaz
     */
    async getNowPlayingMovies(page = 1) {
        try {
            const data = await this.makeRequest('/movie/now_playing', { page });
            
            return {
                results: data.results.map(movie => this.formatMovieBasic(movie)),
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };
        } catch (error) {
            logger.error('Erro ao obter filmes em cartaz:', { error: error.message });
            throw error;
        }
    }

    /**
     * Obtém próximos lançamentos
     */
    async getUpcomingMovies(page = 1) {
        try {
            const data = await this.makeRequest('/movie/upcoming', { page });
            
            return {
                results: data.results.map(movie => this.formatMovieBasic(movie)),
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };
        } catch (error) {
            logger.error('Erro ao obter próximos lançamentos:', { error: error.message });
            throw error;
        }
    }

    /**
     * Obtém filmes mais bem avaliados
     */
    async getTopRatedMovies(page = 1) {
        try {
            const data = await this.makeRequest('/movie/top_rated', { page });
            
            return {
                results: data.results.map(movie => this.formatMovieBasic(movie)),
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };
        } catch (error) {
            logger.error('Erro ao obter filmes mais bem avaliados:', { error: error.message });
            throw error;
        }
    }

    /**
     * Obtém gêneros de filmes
     */
    async getGenres() {
        try {
            const cacheKey = 'genres';
            
            // Verificar cache
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout * 24) { // Cache por 24 horas
                    return cached.data;
                }
            }

            const data = await this.makeRequest('/genre/movie/list');
            
            // Salvar no cache
            this.cache.set(cacheKey, {
                data: data.genres,
                timestamp: Date.now()
            });

            return data.genres;
        } catch (error) {
            logger.error('Erro ao obter gêneros:', { error: error.message });
            throw error;
        }
    }

    /**
     * Descobre filmes por critérios
     */
    async discoverMovies(filters = {}) {
        try {
            const {
                page = 1,
                genre = '',
                year = '',
                sortBy = 'popularity.desc',
                minRating = '',
                maxRating = '',
                minYear = '',
                maxYear = ''
            } = filters;

            const params = { page, sort_by: sortBy };
            
            if (genre) params.with_genres = genre;
            if (year) params.year = year;
            if (minRating) params['vote_average.gte'] = minRating;
            if (maxRating) params['vote_average.lte'] = maxRating;
            if (minYear) params['release_date.gte'] = `${minYear}-01-01`;
            if (maxYear) params['release_date.lte'] = `${maxYear}-12-31`;

            const data = await this.makeRequest('/discover/movie', params);
            
            return {
                results: data.results.map(movie => this.formatMovieBasic(movie)),
                totalResults: data.total_results,
                totalPages: data.total_pages,
                currentPage: data.page
            };
        } catch (error) {
            logger.error('Erro ao descobrir filmes:', { error: error.message, filters });
            throw error;
        }
    }

    /**
     * Formata dados básicos do filme
     */
    formatMovieBasic(movie) {
        return {
            tmdb_id: movie.id,
            title: movie.title,
            original_title: movie.original_title,
            year: movie.release_date ? new Date(movie.release_date).getFullYear() : null,
            release_date: movie.release_date,
            overview: movie.overview,
            poster_path: movie.poster_path ? this.getImageUrl(movie.poster_path, 'w500') : null,
            backdrop_path: movie.backdrop_path ? this.getImageUrl(movie.backdrop_path, 'w1280') : null,
            genre_ids: movie.genre_ids || [],
            vote_average: movie.vote_average,
            vote_count: movie.vote_count,
            popularity: movie.popularity,
            adult: movie.adult,
            original_language: movie.original_language
        };
    }

    /**
     * Formata dados detalhados do filme
     */
    formatMovieDetailed(movie) {
        const basic = this.formatMovieBasic(movie);
        
        return {
            ...basic,
            genres: movie.genres ? movie.genres.map(g => g.name) : [],
            runtime: movie.runtime,
            budget: movie.budget,
            revenue: movie.revenue,
            status: movie.status,
            tagline: movie.tagline,
            homepage: movie.homepage,
            imdb_id: movie.external_ids?.imdb_id || movie.imdb_id,
            
            // Empresas de produção
            production_companies: movie.production_companies ? 
                movie.production_companies.map(company => ({
                    id: company.id,
                    name: company.name,
                    logo_path: company.logo_path ? this.getImageUrl(company.logo_path, 'w200') : null,
                    origin_country: company.origin_country
                })) : [],
            
            // Países de produção
            production_countries: movie.production_countries ? 
                movie.production_countries.map(country => country.name) : [],
            
            // Idiomas
            spoken_languages: movie.spoken_languages ? 
                movie.spoken_languages.map(lang => lang.name) : [],
            
            // Elenco e equipe
            cast: movie.credits && movie.credits.cast ? 
                movie.credits.cast.slice(0, 10).map(person => ({
                    id: person.id,
                    name: person.name,
                    character: person.character,
                    profile_path: person.profile_path ? this.getImageUrl(person.profile_path, 'w185') : null,
                    order: person.order
                })) : [],
            
            // Diretor
            director: movie.credits && movie.credits.crew ? 
                movie.credits.crew.find(person => person.job === 'Director')?.name || '' : '',
            
            // Vídeos (trailers)
            videos: movie.videos && movie.videos.results ? 
                movie.videos.results
                    .filter(video => video.site === 'YouTube' && video.type === 'Trailer')
                    .slice(0, 3)
                    .map(video => ({
                        id: video.id,
                        key: video.key,
                        name: video.name,
                        type: video.type,
                        url: `https://www.youtube.com/watch?v=${video.key}`
                    })) : [],
            
            // Palavras-chave
            keywords: movie.keywords && movie.keywords.keywords ? 
                movie.keywords.keywords.slice(0, 10).map(keyword => keyword.name) : [],
            
            // Filmes similares
            similar: movie.similar && movie.similar.results ?
                movie.similar.results.slice(0, 5).map(m => this.formatMovieBasic(m)) : [],
            
            // Recomendações
            recommendations: movie.recommendations && movie.recommendations.results ?
                movie.recommendations.results.slice(0, 5).map(m => this.formatMovieBasic(m)) : []
        };
    }

    /**
     * Constrói URL completa para imagens
     */
    getImageUrl(imagePath, size = 'w500') {
        if (!imagePath) return null;
        return `${this.imageBaseUrl}/${size}${imagePath}`;
    }

    /**
     * Obtém configuração da API
     */
    async getConfiguration() {
        try {
            const cacheKey = 'configuration';
            
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout * 24) {
                    return cached.data;
                }
            }

            const data = await this.makeRequest('/configuration');
            
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });

            return data;
        } catch (error) {
            logger.error('Erro ao obter configuração:', { error: error.message });
            throw error;
        }
    }

    /**
     * Limpa cache
     */
    clearCache() {
        this.cache.clear();
        logger.info('Cache TMDb limpo');
    }

    /**
     * Obtém estatísticas do cache
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

module.exports = new TMDbService();
