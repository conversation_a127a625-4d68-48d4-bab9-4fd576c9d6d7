/**
 * <PERSON><PERSON>s da API TMDb
 */

const express = require('express');
const { query, param, validationResult } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const tmdbService = require('../services/tmdbService');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Middleware de validação
 */
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Dados inválidos',
            details: errors.array()
        });
    }
    next();
};

/**
 * GET /api/tmdb/search
 * Busca filmes no TMDb
 */
router.get('/search', [
    query('q').notEmpty().isLength({ min: 1, max: 100 }).withMessage('Query é obrigatória e deve ter até 100 caracteres'),
    query('page').optional().isInt({ min: 1, max: 1000 }).withMessage('Página deve ser entre 1 e 1000')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const query = req.query.q;
    const page = parseInt(req.query.page) || 1;

    const results = await tmdbService.searchMovies(query, page);
    
    logger.activity('search', 'tmdb', { query, page, results: results.results.length }, req.ip);

    res.json({
        success: true,
        data: results
    });
}));

/**
 * GET /api/tmdb/movie/:id
 * Obtém detalhes de um filme do TMDb
 */
router.get('/movie/:id', [
    param('id').isInt({ min: 1 }).withMessage('ID deve ser um número positivo')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const movieId = req.params.id;

    const movie = await tmdbService.getMovieDetails(movieId);
    
    logger.activity('view', 'tmdb_movie', { tmdbId: movieId, title: movie.title }, req.ip);

    res.json({
        success: true,
        data: movie
    });
}));

/**
 * GET /api/tmdb/popular
 * Obtém filmes populares do TMDb
 */
router.get('/popular', [
    query('page').optional().isInt({ min: 1, max: 1000 }).withMessage('Página deve ser entre 1 e 1000')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;

    const results = await tmdbService.getPopularMovies(page);
    
    logger.activity('list', 'tmdb_popular', { page, results: results.results.length }, req.ip);

    res.json({
        success: true,
        data: results
    });
}));

/**
 * GET /api/tmdb/now-playing
 * Obtém filmes em cartaz do TMDb
 */
router.get('/now-playing', [
    query('page').optional().isInt({ min: 1, max: 1000 }).withMessage('Página deve ser entre 1 e 1000')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;

    const results = await tmdbService.getNowPlayingMovies(page);
    
    logger.activity('list', 'tmdb_now_playing', { page, results: results.results.length }, req.ip);

    res.json({
        success: true,
        data: results
    });
}));

/**
 * GET /api/tmdb/upcoming
 * Obtém próximos lançamentos do TMDb
 */
router.get('/upcoming', [
    query('page').optional().isInt({ min: 1, max: 1000 }).withMessage('Página deve ser entre 1 e 1000')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;

    const results = await tmdbService.getUpcomingMovies(page);
    
    logger.activity('list', 'tmdb_upcoming', { page, results: results.results.length }, req.ip);

    res.json({
        success: true,
        data: results
    });
}));

/**
 * GET /api/tmdb/top-rated
 * Obtém filmes mais bem avaliados do TMDb
 */
router.get('/top-rated', [
    query('page').optional().isInt({ min: 1, max: 1000 }).withMessage('Página deve ser entre 1 e 1000')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;

    const results = await tmdbService.getTopRatedMovies(page);
    
    logger.activity('list', 'tmdb_top_rated', { page, results: results.results.length }, req.ip);

    res.json({
        success: true,
        data: results
    });
}));

/**
 * GET /api/tmdb/genres
 * Obtém lista de gêneros do TMDb
 */
router.get('/genres', asyncHandler(async (req, res) => {
    const genres = await tmdbService.getGenres();
    
    logger.activity('list', 'tmdb_genres', { count: genres.length }, req.ip);

    res.json({
        success: true,
        data: genres
    });
}));

/**
 * GET /api/tmdb/discover
 * Descobre filmes por critérios
 */
router.get('/discover', [
    query('page').optional().isInt({ min: 1, max: 1000 }).withMessage('Página deve ser entre 1 e 1000'),
    query('genre').optional().isInt({ min: 1 }).withMessage('Gênero deve ser um ID válido'),
    query('year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Ano inválido'),
    query('sort_by').optional().isIn([
        'popularity.desc', 'popularity.asc',
        'release_date.desc', 'release_date.asc',
        'revenue.desc', 'revenue.asc',
        'primary_release_date.desc', 'primary_release_date.asc',
        'original_title.desc', 'original_title.asc',
        'vote_average.desc', 'vote_average.asc',
        'vote_count.desc', 'vote_count.asc'
    ]).withMessage('Ordenação inválida'),
    query('min_rating').optional().isFloat({ min: 0, max: 10 }).withMessage('Avaliação mínima deve ser entre 0 e 10'),
    query('max_rating').optional().isFloat({ min: 0, max: 10 }).withMessage('Avaliação máxima deve ser entre 0 e 10'),
    query('min_year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Ano mínimo inválido'),
    query('max_year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Ano máximo inválido')
], handleValidationErrors, asyncHandler(async (req, res) => {
    const filters = {
        page: parseInt(req.query.page) || 1,
        genre: req.query.genre || '',
        year: req.query.year || '',
        sortBy: req.query.sort_by || 'popularity.desc',
        minRating: req.query.min_rating || '',
        maxRating: req.query.max_rating || '',
        minYear: req.query.min_year || '',
        maxYear: req.query.max_year || ''
    };

    const results = await tmdbService.discoverMovies(filters);
    
    logger.activity('discover', 'tmdb', { filters, results: results.results.length }, req.ip);

    res.json({
        success: true,
        data: results
    });
}));

/**
 * GET /api/tmdb/configuration
 * Obtém configuração da API TMDb
 */
router.get('/configuration', asyncHandler(async (req, res) => {
    const config = await tmdbService.getConfiguration();
    
    res.json({
        success: true,
        data: config
    });
}));

/**
 * POST /api/tmdb/clear-cache
 * Limpa cache do TMDb
 */
router.post('/clear-cache', asyncHandler(async (req, res) => {
    tmdbService.clearCache();
    
    logger.activity('clear_cache', 'tmdb', {}, req.ip);

    res.json({
        success: true,
        message: 'Cache TMDb limpo com sucesso'
    });
}));

/**
 * GET /api/tmdb/cache-stats
 * Obtém estatísticas do cache
 */
router.get('/cache-stats', asyncHandler(async (req, res) => {
    const stats = tmdbService.getCacheStats();
    
    res.json({
        success: true,
        data: stats
    });
}));

module.exports = router;
