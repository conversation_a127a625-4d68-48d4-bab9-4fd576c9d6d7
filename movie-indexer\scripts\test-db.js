/**
 * Script de teste do banco de dados
 */

require('dotenv').config();
const database = require('../src/config/database');

async function testDatabase() {
    try {
        console.log('🧪 Testando conexão com banco de dados...');
        
        // Inicializar banco
        await database.initializeDatabase();
        console.log('✅ Banco inicializado com sucesso!');
        
        // Testar inserção simples
        const result = await database.run(`
            INSERT INTO movies (title, year, overview, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?)
        `, ['Filme Teste', 2024, 'Descrição do filme teste', new Date().toISOString(), new Date().toISOString()]);
        
        console.log('✅ Filme teste inserido:', result);
        
        // Buscar filme
        const movie = await database.get('SELECT * FROM movies WHERE id = ?', [result.id]);
        console.log('✅ Filme encontrado:', movie);
        
        // Mostrar estatísticas
        const stats = await database.getStats();
        console.log('📊 Estatísticas:', stats);
        
        console.log('✅ Teste concluído com sucesso!');
        
    } catch (error) {
        console.error('❌ Erro no teste:', error);
    } finally {
        await database.close();
        process.exit(0);
    }
}

testDatabase();
