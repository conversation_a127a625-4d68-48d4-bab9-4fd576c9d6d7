const addonManager = require('./addonManager');
const subtitleService = require('./subtitleService');
const logger = require('../utils/logger');

/**
 * Serviço de Streaming
 * Coordena busca de torrents e legendas para streaming
 */
class StreamingService {
    constructor() {
        this.activeStreams = new Map();
        this.streamHistory = [];
    }

    /**
     * Inicializa o serviço e registra addons
     */
    async initialize() {
        try {
            // Registrar addon Torrentio
            const TorrentioAddon = require('../addons/torrentio');
            const torrentioInstance = new TorrentioAddon();
            
            addonManager.registerAddon('torrentio', torrentioInstance);
            
            // Testar conectividade
            const connectionTest = await torrentioInstance.testConnection();
            if (connectionTest.success) {
                logger.info(`Torrentio conectado: ${connectionTest.name} v${connectionTest.version}`);
            } else {
                logger.warn(`Erro ao conectar com Torrentio: ${connectionTest.error}`);
            }

            logger.info('Serviço de streaming inicializado');
        } catch (error) {
            logger.error('Erro ao inicializar serviço de streaming:', error.message);
        }
    }

    /**
     * Busca opções de streaming para um filme
     */
    async getStreamingOptions(movieData) {
        try {
            logger.info(`Buscando opções de streaming para: ${movieData.title}`);

            // Buscar torrents
            logger.info('Iniciando busca de torrents...');
            const torrents = await this.searchTorrents(movieData);
            logger.info(`Encontrados ${torrents.length} torrents`);

            // Buscar legendas
            logger.info('Iniciando busca de legendas...');
            const subtitles = await this.searchSubtitles(movieData);
            logger.info(`Encontradas legendas para ${Object.keys(subtitles).length} idiomas`);

            // Organizar por qualidade
            const streamingOptions = this.organizeStreamingOptions(torrents, subtitles);

            // Salvar no histórico
            this.addToHistory(movieData, streamingOptions);

            return {
                success: true,
                movie: {
                    id: movieData.id,
                    title: movieData.title,
                    year: movieData.year,
                    imdb_id: movieData.imdb_id,
                    tmdb_id: movieData.tmdb_id
                },
                options: streamingOptions,
                subtitles: subtitles,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            logger.error('Erro ao buscar opções de streaming:', error.message);
            return {
                success: false,
                error: error.message,
                movie: movieData,
                options: [],
                subtitles: {},
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Busca torrents para o filme
     */
    async searchTorrents(movieData) {
        try {
            const torrents = await addonManager.searchMovie(movieData);
            
            logger.info(`Encontrados ${torrents.length} torrents para ${movieData.title}`);
            
            return torrents;
        } catch (error) {
            logger.error('Erro na busca de torrents:', error.message);
            return [];
        }
    }

    /**
     * Busca legendas para o filme
     */
    async searchSubtitles(movieData) {
        try {
            const languages = ['pt', 'pt-BR', 'en'];
            const subtitles = await subtitleService.searchSubtitles(movieData, languages);
            
            const totalSubtitles = Object.values(subtitles).reduce((sum, subs) => sum + subs.length, 0);
            logger.info(`Encontradas ${totalSubtitles} legendas para ${movieData.title}`);
            
            return subtitles;
        } catch (error) {
            logger.error('Erro na busca de legendas:', error.message);
            return {};
        }
    }

    /**
     * Organiza opções de streaming por qualidade
     */
    organizeStreamingOptions(torrents, subtitles) {
        const qualityGroups = {
            '4K': [],
            '1080p': [],
            '720p': [],
            '480p': [],
            'Other': []
        };

        torrents.forEach(torrent => {
            const quality = torrent.quality || 'Other';
            const group = qualityGroups[quality] || qualityGroups['Other'];
            
            group.push({
                id: this.generateStreamId(),
                title: torrent.title,
                magnet: torrent.magnet,
                quality: quality,
                size: torrent.size,
                seeders: torrent.seeders,
                provider: torrent.provider || torrent.addon,
                hash: torrent.hash,
                source: torrent.source
            });
        });

        // Remover grupos vazios e limitar resultados
        const result = {};
        Object.keys(qualityGroups).forEach(quality => {
            if (qualityGroups[quality].length > 0) {
                // Limitar a 5 torrents por qualidade, ordenados por seeders
                result[quality] = qualityGroups[quality]
                    .sort((a, b) => (b.seeders || 0) - (a.seeders || 0))
                    .slice(0, 5);
            }
        });

        return result;
    }

    /**
     * Gera ID único para stream
     */
    generateStreamId() {
        return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Inicia streaming de um torrent
     */
    async startStream(streamId, magnetLink, options = {}) {
        try {
            const {
                quality = 'unknown',
                movieId = null,
                subtitleLanguage = null
            } = options;

            // Registrar stream ativo
            const streamData = {
                id: streamId,
                magnet: magnetLink,
                quality: quality,
                movieId: movieId,
                startTime: new Date(),
                status: 'starting',
                subtitleLanguage: subtitleLanguage
            };

            this.activeStreams.set(streamId, streamData);

            logger.info(`Iniciando stream ${streamId} com qualidade ${quality}`);

            // Retornar dados para o frontend iniciar WebTorrent
            return {
                success: true,
                streamId: streamId,
                magnet: magnetLink,
                quality: quality,
                message: 'Stream iniciado com sucesso'
            };
        } catch (error) {
            logger.error('Erro ao iniciar stream:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Para um stream ativo
     */
    async stopStream(streamId) {
        try {
            if (this.activeStreams.has(streamId)) {
                const streamData = this.activeStreams.get(streamId);
                streamData.status = 'stopped';
                streamData.endTime = new Date();
                
                this.activeStreams.delete(streamId);
                
                logger.info(`Stream ${streamId} parado`);
                
                return {
                    success: true,
                    message: 'Stream parado com sucesso'
                };
            } else {
                return {
                    success: false,
                    error: 'Stream não encontrado'
                };
            }
        } catch (error) {
            logger.error('Erro ao parar stream:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Obtém status de um stream
     */
    getStreamStatus(streamId) {
        if (this.activeStreams.has(streamId)) {
            return this.activeStreams.get(streamId);
        }
        return null;
    }

    /**
     * Lista streams ativos
     */
    getActiveStreams() {
        return Array.from(this.activeStreams.values());
    }

    /**
     * Baixa legenda para um filme
     */
    async downloadSubtitle(subtitleData) {
        try {
            const subtitle = await subtitleService.downloadSubtitle(subtitleData);
            
            // Converter para WebVTT se necessário
            if (subtitle.format === 'srt') {
                subtitle.vttContent = subtitleService.convertSrtToVtt(subtitle.content);
            }
            
            return {
                success: true,
                subtitle: subtitle
            };
        } catch (error) {
            logger.error('Erro ao baixar legenda:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Adiciona ao histórico
     */
    addToHistory(movieData, streamingOptions) {
        const historyEntry = {
            movieId: movieData.id,
            title: movieData.title,
            timestamp: new Date(),
            optionsCount: Object.values(streamingOptions).reduce((sum, opts) => sum + opts.length, 0)
        };

        this.streamHistory.unshift(historyEntry);
        
        // Manter apenas os últimos 100 registros
        if (this.streamHistory.length > 100) {
            this.streamHistory = this.streamHistory.slice(0, 100);
        }
    }

    /**
     * Obtém histórico de streaming
     */
    getStreamingHistory() {
        return this.streamHistory;
    }
}

module.exports = new StreamingService();
