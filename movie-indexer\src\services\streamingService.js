const addonManager = require('./addonManager');
const subtitleService = require('./subtitleService');
const cacheService = require('./cacheService');
const qualityDetectionService = require('./qualityDetectionService');
const StreamingHistory = require('../models/StreamingHistory');
const logger = require('../utils/logger');

/**
 * Serviço de Streaming
 * Coordena busca de torrents e legendas para streaming
 */
class StreamingService {
    constructor() {
        this.activeStreams = new Map();
        this.streamHistory = [];
    }

    /**
     * Inicializa o serviço e registra addons
     */
    async initialize() {
        try {
            // Registrar addon Torrentio
            const TorrentioAddon = require('../addons/torrentio');
            const torrentioInstance = new TorrentioAddon();
            
            addonManager.registerAddon('torrentio', torrentioInstance);
            
            // Testar conectividade
            const connectionTest = await torrentioInstance.testConnection();
            if (connectionTest.success) {
                logger.info(`Torrentio conectado: ${connectionTest.name} v${connectionTest.version}`);
            } else {
                logger.warn(`Erro ao conectar com Torrentio: ${connectionTest.error}`);
            }

            logger.info('Serviço de streaming inicializado');
        } catch (error) {
            logger.error('Erro ao inicializar serviço de streaming:', error.message);
        }
    }

    /**
     * Busca opções de streaming para um filme
     */
    async getStreamingOptions(movieData) {
        try {
            logger.info(`Buscando opções de streaming para: ${movieData.title}`);

            // Verificar cache primeiro
            const cached = cacheService.getStreamingOptions(movieData.id);
            if (cached) {
                logger.info(`Opções encontradas no cache para: ${movieData.title}`);
                return {
                    success: true,
                    movie: {
                        id: movieData.id,
                        title: movieData.title,
                        year: movieData.year,
                        imdb_id: movieData.imdb_id,
                        tmdb_id: movieData.tmdb_id
                    },
                    options: cached.options,
                    subtitles: cached.subtitles,
                    timestamp: cached.timestamp,
                    cached: true
                };
            }

            // Buscar torrents
            logger.info('Iniciando busca de torrents...');
            const torrents = await this.searchTorrents(movieData);
            logger.info(`Encontrados ${torrents.length} torrents`);

            // Buscar legendas
            logger.info('Iniciando busca de legendas...');
            const subtitles = await this.searchSubtitles(movieData);
            logger.info(`Encontradas legendas para ${Object.keys(subtitles).length} idiomas`);

            // Organizar por qualidade
            const streamingOptions = await this.organizeStreamingOptions(torrents, subtitles);

            // Salvar no cache
            cacheService.setStreamingOptions(movieData.id, streamingOptions, subtitles);

            // Salvar no histórico
            this.addToHistory(movieData, streamingOptions);

            return {
                success: true,
                movie: {
                    id: movieData.id,
                    title: movieData.title,
                    year: movieData.year,
                    imdb_id: movieData.imdb_id,
                    tmdb_id: movieData.tmdb_id
                },
                options: streamingOptions,
                subtitles: subtitles,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            logger.error('Erro ao buscar opções de streaming:', error.message);
            return {
                success: false,
                error: error.message,
                movie: movieData,
                options: [],
                subtitles: {},
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * Busca torrents para o filme
     */
    async searchTorrents(movieData) {
        try {
            const torrents = await addonManager.searchMovie(movieData);
            
            logger.info(`Encontrados ${torrents.length} torrents para ${movieData.title}`);
            
            return torrents;
        } catch (error) {
            logger.error('Erro na busca de torrents:', error.message);
            return [];
        }
    }

    /**
     * Busca legendas para o filme
     */
    async searchSubtitles(movieData) {
        try {
            const languages = ['pt', 'pt-BR', 'en'];
            const subtitles = await subtitleService.searchSubtitles(movieData, languages);
            
            const totalSubtitles = Object.values(subtitles).reduce((sum, subs) => sum + subs.length, 0);
            logger.info(`Encontradas ${totalSubtitles} legendas para ${movieData.title}`);
            
            return subtitles;
        } catch (error) {
            logger.error('Erro na busca de legendas:', error.message);
            return {};
        }
    }

    /**
     * Organiza opções de streaming por qualidade
     */
    async organizeStreamingOptions(torrents, subtitles) {
        const qualityGroups = {
            '4K': [],
            '1080p': [],
            '720p': [],
            '480p': [],
            'Other': []
        };

        for (const torrent of torrents) {
            // Usar detecção avançada de qualidade
            const qualityInfo = await qualityDetectionService.detectQuality(
                torrent.title,
                torrent.magnet
            );

            const quality = qualityInfo.detected_quality;
            const group = qualityGroups[quality] || qualityGroups['Other'];

            group.push({
                id: this.generateStreamId(),
                title: torrent.title,
                magnet: torrent.magnet,
                quality: quality,
                resolution: qualityInfo.resolution,
                codec: qualityInfo.codec,
                source: qualityInfo.source,
                size: torrent.size,
                seeders: torrent.seeders,
                provider: torrent.provider || torrent.addon,
                hash: torrent.hash,
                confidence: qualityInfo.confidence_score,
                detectionMethod: qualityInfo.detection_method
            });
        }

        // Remover grupos vazios e limitar resultados
        const result = {};
        Object.keys(qualityGroups).forEach(quality => {
            if (qualityGroups[quality].length > 0) {
                // Ordenar por confiança e seeders, limitar a 5 por qualidade
                result[quality] = qualityGroups[quality]
                    .sort((a, b) => {
                        // Primeiro por confiança da detecção
                        const confidenceDiff = (b.confidence || 0) - (a.confidence || 0);
                        if (Math.abs(confidenceDiff) > 0.1) {
                            return confidenceDiff;
                        }
                        // Depois por seeders
                        return (b.seeders || 0) - (a.seeders || 0);
                    })
                    .slice(0, 5);
            }
        });

        return result;
    }

    /**
     * Gera ID único para stream
     */
    generateStreamId() {
        return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Inicia streaming de um torrent
     */
    async startStream(streamId, magnetLink, options = {}) {
        try {
            const {
                quality = 'unknown',
                movieId = null,
                subtitleLanguage = null
            } = options;

            // Registrar stream ativo
            const streamData = {
                id: streamId,
                magnet: magnetLink,
                quality: quality,
                movieId: movieId,
                startTime: new Date(),
                status: 'starting',
                subtitleLanguage: subtitleLanguage
            };

            this.activeStreams.set(streamId, streamData);

            logger.info(`Iniciando stream ${streamId} com qualidade ${quality}`);

            // Retornar dados para o frontend iniciar WebTorrent
            return {
                success: true,
                streamId: streamId,
                magnet: magnetLink,
                quality: quality,
                message: 'Stream iniciado com sucesso'
            };
        } catch (error) {
            logger.error('Erro ao iniciar stream:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Para um stream ativo
     */
    async stopStream(streamId) {
        try {
            if (this.activeStreams.has(streamId)) {
                const streamData = this.activeStreams.get(streamId);
                streamData.status = 'stopped';
                streamData.endTime = new Date();
                
                this.activeStreams.delete(streamId);
                
                logger.info(`Stream ${streamId} parado`);
                
                return {
                    success: true,
                    message: 'Stream parado com sucesso'
                };
            } else {
                return {
                    success: false,
                    error: 'Stream não encontrado'
                };
            }
        } catch (error) {
            logger.error('Erro ao parar stream:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Obtém status de um stream
     */
    getStreamStatus(streamId) {
        if (this.activeStreams.has(streamId)) {
            return this.activeStreams.get(streamId);
        }
        return null;
    }

    /**
     * Lista streams ativos
     */
    getActiveStreams() {
        return Array.from(this.activeStreams.values());
    }

    /**
     * Baixa legenda para um filme
     */
    async downloadSubtitle(subtitleData) {
        try {
            const subtitle = await subtitleService.downloadSubtitle(subtitleData);
            
            // Converter para WebVTT se necessário
            if (subtitle.format === 'srt') {
                subtitle.vttContent = subtitleService.convertSrtToVtt(subtitle.content);
            }
            
            return {
                success: true,
                subtitle: subtitle
            };
        } catch (error) {
            logger.error('Erro ao baixar legenda:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Adiciona ao histórico
     */
    addToHistory(movieData, streamingOptions) {
        const historyEntry = {
            movieId: movieData.id,
            title: movieData.title,
            timestamp: new Date(),
            optionsCount: Object.values(streamingOptions).reduce((sum, opts) => sum + opts.length, 0)
        };

        this.streamHistory.unshift(historyEntry);
        
        // Manter apenas os últimos 100 registros
        if (this.streamHistory.length > 100) {
            this.streamHistory = this.streamHistory.slice(0, 100);
        }
    }

    /**
     * Obtém histórico de streaming (método legado)
     */
    getStreamingHistory() {
        return this.streamHistory;
    }

    /**
     * Inicia sessão de streaming
     */
    async startStreamingSession(movieData, torrentData) {
        try {
            const session = new StreamingHistory({
                movie_id: movieData.id,
                title: movieData.title,
                quality: torrentData.quality,
                provider: torrentData.provider,
                magnet_link: torrentData.magnet,
                started_at: new Date().toISOString()
            });

            await session.save();
            logger.info(`Sessão de streaming iniciada: ${movieData.title} (${torrentData.quality})`);
            return session;
        } catch (error) {
            logger.error('Erro ao iniciar sessão de streaming:', error.message);
            throw error;
        }
    }

    /**
     * Atualiza progresso da sessão
     */
    async updateStreamingProgress(sessionId, progressPercent, durationSeconds = null) {
        try {
            const session = await StreamingHistory.findById(sessionId);
            if (session) {
                await session.updateProgress(progressPercent, durationSeconds);
                logger.debug(`Progresso atualizado: ${progressPercent}% (sessão ${sessionId})`);
                return session;
            }
            return null;
        } catch (error) {
            logger.error('Erro ao atualizar progresso:', error.message);
            throw error;
        }
    }

    /**
     * Finaliza sessão de streaming
     */
    async endStreamingSession(sessionId, userRating = null, userNotes = null) {
        try {
            const session = await StreamingHistory.findById(sessionId);
            if (session) {
                await session.endSession(userRating, userNotes);
                logger.info(`Sessão finalizada: ${session.title} (${session.quality})`);
                return session;
            }
            return null;
        } catch (error) {
            logger.error('Erro ao finalizar sessão:', error.message);
            throw error;
        }
    }

    /**
     * Obtém histórico de streaming do banco
     */
    async getStreamingHistoryFromDB(movieId = null, limit = 20, offset = 0) {
        try {
            if (movieId) {
                return await StreamingHistory.findByMovieId(movieId, limit);
            } else {
                return await StreamingHistory.findRecent(limit, offset);
            }
        } catch (error) {
            logger.error('Erro ao obter histórico:', error.message);
            return [];
        }
    }

    /**
     * Obtém estatísticas de streaming
     */
    async getStreamingStats() {
        try {
            const [historyStats, cacheStats] = await Promise.all([
                StreamingHistory.getStats(),
                qualityDetectionService.getCacheStats()
            ]);

            return {
                history: historyStats,
                qualityCache: cacheStats,
                cache: cacheService.getStats()
            };
        } catch (error) {
            logger.error('Erro ao obter estatísticas:', error.message);
            return {
                history: {},
                qualityCache: {},
                cache: {}
            };
        }
    }
}

module.exports = new StreamingService();
