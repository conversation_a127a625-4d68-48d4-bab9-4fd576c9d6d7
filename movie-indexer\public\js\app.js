/**
 * Movie Indexer - A<PERSON>lica<PERSON> Principal
 */

class MovieIndexer {
    constructor() {
        this.currentTab = 'catalog';
        this.currentPage = 1;
        this.currentFilters = {};
        this.currentMovie = null;
        this.genres = [];
        
        this.init();
    }

    /**
     * Inicializa a aplicação
     */
    async init() {
        console.log('🎬 Inicializando Movie Indexer...');
        
        this.setupEventListeners();
        this.setupTabNavigation();
        await this.loadInitialData();
        
        console.log('✅ Movie Indexer inicializado!');
    }

    /**
     * Configura event listeners
     */
    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Search
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        if (searchInput && searchBtn) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.searchMovies();
            });
            searchBtn.addEventListener('click', () => this.searchMovies());
        }

        // Filters
        this.setupFilters();

        // Modals
        this.setupModals();

        // Forms
        this.setupForms();

        // Release tabs
        this.setupReleaseTabs();
    }

    /**
     * Configura navegação entre abas
     */
    setupTabNavigation() {
        const navBtns = document.querySelectorAll('.nav-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        navBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // Remove active de todas as abas
                navBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Ativa aba selecionada
                btn.classList.add('active');
                document.getElementById(`${targetTab}-tab`).classList.add('active');
                
                this.currentTab = targetTab;
                this.handleTabChange(targetTab);
            });
        });
    }

    /**
     * Configura filtros
     */
    setupFilters() {
        // Filtros do catálogo
        const catalogSearch = document.getElementById('catalogSearch');
        const resolutionFilter = document.getElementById('resolutionFilter');
        const genreFilter = document.getElementById('genreFilter');

        if (catalogSearch) {
            catalogSearch.addEventListener('input', ui.debounce(() => {
                this.filterCatalog();
            }, 300));
        }

        if (resolutionFilter) {
            resolutionFilter.addEventListener('change', () => this.filterCatalog());
        }

        if (genreFilter) {
            genreFilter.addEventListener('change', () => this.filterCatalog());
        }

        // Filtros de busca
        const searchFilters = ['searchYear', 'searchGenre', 'searchSort'];
        searchFilters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => this.searchMovies());
            }
        });

        // Filtros de links
        const linkFilters = ['linkProviderFilter', 'linkResolutionFilter', 'linkStatusFilter'];
        linkFilters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => this.loadLinks());
            }
        });
    }

    /**
     * Configura modais
     */
    setupModals() {
        // Fechar modais
        const closeButtons = document.querySelectorAll('.modal-close');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const modal = btn.closest('.modal');
                if (modal) ui.hideModal(modal.id);
            });
        });

        // Fechar modal clicando fora
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                ui.hideModal(e.target.id);
            }
        });

        // Botões de ação
        const addMovieBtn = document.getElementById('addMovieBtn');
        const addLinkBtn = document.getElementById('addLinkBtn');

        if (addMovieBtn) {
            addMovieBtn.addEventListener('click', () => this.showAddMovieModal());
        }

        if (addLinkBtn) {
            addLinkBtn.addEventListener('click', () => this.showAddLinkModal());
        }
    }

    /**
     * Configura formulários
     */
    setupForms() {
        // Formulário de link
        const linkForm = document.getElementById('linkForm');
        if (linkForm) {
            linkForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLinkSubmit();
            });
        }

        const cancelLinkBtn = document.getElementById('cancelLinkBtn');
        if (cancelLinkBtn) {
            cancelLinkBtn.addEventListener('click', () => {
                ui.hideModal('linkModal');
            });
        }
    }

    /**
     * Configura abas de lançamentos
     */
    setupReleaseTabs() {
        const releaseTabs = document.querySelectorAll('[data-release-type]');
        releaseTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active de todas as abas
                releaseTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Carrega dados do tipo selecionado
                this.loadReleases(tab.dataset.releaseType);
            });
        });
    }

    /**
     * Carrega dados iniciais
     */
    async loadInitialData() {
        try {
            // Carregar gêneros
            await this.loadGenres();
            
            // Carregar dados da aba ativa
            await this.handleTabChange(this.currentTab);
            
            // Carregar estatísticas
            await this.updateStats();
        } catch (error) {
            console.error('Erro ao carregar dados iniciais:', error);
            ui.showToast('Erro', 'Erro ao carregar dados iniciais', 'error');
        }
    }

    /**
     * Manipula mudança de aba
     */
    async handleTabChange(tab) {
        this.currentPage = 1;
        this.currentFilters = {};

        switch (tab) {
            case 'catalog':
                await this.loadCatalog();
                break;
            case 'search':
                this.showSearchWelcome();
                break;
            case 'releases':
                await this.loadReleases('popular');
                break;
            case 'links':
                await this.loadLinks();
                break;
        }
    }

    /**
     * Carrega gêneros
     */
    async loadGenres() {
        try {
            const response = await api.getTmdbGenres();
            this.genres = response.data;
            
            // Preencher selects de gênero
            this.populateGenreSelects();
        } catch (error) {
            console.error('Erro ao carregar gêneros:', error);
        }
    }

    /**
     * Preenche selects de gênero
     */
    populateGenreSelects() {
        const selects = ['genreFilter', 'searchGenre'];
        
        selects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                // Limpar opções existentes (exceto a primeira)
                while (select.children.length > 1) {
                    select.removeChild(select.lastChild);
                }
                
                // Adicionar gêneros
                this.genres.forEach(genre => {
                    const option = document.createElement('option');
                    option.value = genre.id;
                    option.textContent = genre.name;
                    select.appendChild(option);
                });
            }
        });
    }

    /**
     * Carrega catálogo
     */
    async loadCatalog() {
        try {
            ui.showLoading('Carregando catálogo...');
            
            const params = {
                page: this.currentPage,
                limit: 20,
                ...this.currentFilters
            };
            
            const response = await api.getMovies(params);
            this.displayMovies(response.data, 'catalogGrid');
            this.displayPagination(response.pagination, 'catalogPagination', () => this.loadCatalog());
            
        } catch (error) {
            console.error('Erro ao carregar catálogo:', error);
            ui.showToast('Erro', 'Erro ao carregar catálogo', 'error');
            this.showEmptyState('catalogGrid', 'Erro ao carregar catálogo');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Filtra catálogo
     */
    async filterCatalog() {
        const search = document.getElementById('catalogSearch')?.value || '';
        const resolution = document.getElementById('resolutionFilter')?.value || '';
        const genre = document.getElementById('genreFilter')?.value || '';

        this.currentFilters = {
            search: search,
            resolution: resolution,
            genre: genre
        };
        
        this.currentPage = 1;
        await this.loadCatalog();
    }

    /**
     * Busca filmes
     */
    async searchMovies() {
        const query = document.getElementById('searchInput')?.value?.trim();
        
        if (!query) {
            this.showSearchWelcome();
            return;
        }

        try {
            ui.showLoading('Buscando filmes...');
            
            const year = document.getElementById('searchYear')?.value || '';
            const genre = document.getElementById('searchGenre')?.value || '';
            const sort = document.getElementById('searchSort')?.value || 'popularity.desc';
            
            let response;
            
            if (year || genre || sort !== 'popularity.desc') {
                // Usar discover se há filtros
                response = await api.discoverTmdb({
                    page: this.currentPage,
                    year: year,
                    genre: genre,
                    sort_by: sort
                });
            } else {
                // Usar search normal
                response = await api.searchTmdb(query, this.currentPage);
            }
            
            this.displayMovies(response.data.results, 'searchResults', 'tmdb');
            this.displayPagination({
                page: response.data.currentPage,
                pages: response.data.totalPages,
                total: response.data.totalResults
            }, 'searchPagination', () => this.searchMovies());
            
        } catch (error) {
            console.error('Erro na busca:', error);
            ui.showToast('Erro', 'Erro ao buscar filmes', 'error');
            this.showEmptyState('searchResults', 'Erro na busca');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Carrega lançamentos
     */
    async loadReleases(type = 'popular') {
        try {
            ui.showLoading('Carregando lançamentos...');
            
            let response;
            switch (type) {
                case 'popular':
                    response = await api.getTmdbPopular(this.currentPage);
                    break;
                case 'now-playing':
                    response = await api.getTmdbNowPlaying(this.currentPage);
                    break;
                case 'upcoming':
                    response = await api.getTmdbUpcoming(this.currentPage);
                    break;
                case 'top-rated':
                    response = await api.getTmdbTopRated(this.currentPage);
                    break;
            }
            
            this.displayMovies(response.data.results, 'releasesGrid', 'tmdb');
            this.displayPagination({
                page: response.data.currentPage,
                pages: response.data.totalPages,
                total: response.data.totalResults
            }, 'releasesPagination', () => this.loadReleases(type));
            
        } catch (error) {
            console.error('Erro ao carregar lançamentos:', error);
            ui.showToast('Erro', 'Erro ao carregar lançamentos', 'error');
            this.showEmptyState('releasesGrid', 'Erro ao carregar lançamentos');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Carrega links
     */
    async loadLinks() {
        try {
            ui.showLoading('Carregando links...');

            const provider = document.getElementById('linkProviderFilter')?.value || '';
            const resolution = document.getElementById('linkResolutionFilter')?.value || '';
            const verified = document.getElementById('linkStatusFilter')?.value || '';

            const params = {
                page: this.currentPage,
                limit: 20,
                provider: provider,
                resolution: resolution,
                verified: verified
            };

            const response = await api.getLinks(params);
            this.displayLinks(response.data);
            this.displayPagination(response.pagination, 'linksPagination', () => this.loadLinks());

        } catch (error) {
            console.error('Erro ao carregar links:', error);
            ui.showToast('Erro', 'Erro ao carregar links', 'error');
            this.showEmptyState('linksList', 'Erro ao carregar links');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Exibe filmes
     */
    displayMovies(movies, containerId, type = 'catalog') {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!movies || movies.length === 0) {
            this.showEmptyState(containerId, 'Nenhum filme encontrado');
            return;
        }

        container.innerHTML = '';

        movies.forEach(movie => {
            const movieCard = this.createMovieCard(movie, type);
            container.appendChild(movieCard);
        });
    }

    /**
     * Cria card de filme
     */
    createMovieCard(movie, type = 'catalog') {
        const card = document.createElement('div');
        card.className = 'movie-card';

        const posterUrl = movie.poster_path || 'https://via.placeholder.com/300x450/334155/cbd5e1?text=Sem+Poster';
        const rating = movie.vote_average ? ui.formatRating(movie.vote_average) : 'N/A';
        const year = movie.year || (movie.release_date ? new Date(movie.release_date).getFullYear() : 'N/A');
        const genres = Array.isArray(movie.genres) ? movie.genres.slice(0, 3) :
                      Array.isArray(movie.genre_ids) ? movie.genre_ids.slice(0, 3).map(id => {
                          const genre = this.genres.find(g => g.id === id);
                          return genre ? genre.name : '';
                      }).filter(Boolean) : [];
        const synopsis = ui.truncateText(movie.overview || 'Sinopse não disponível', 150);

        card.innerHTML = `
            <div class="movie-poster">
                <img src="${posterUrl}" alt="${movie.title}" loading="lazy">
                <div class="movie-rating">⭐ ${rating}</div>
            </div>
            <div class="movie-info">
                <h3 class="movie-title">${movie.title}</h3>
                <div class="movie-year">${year}</div>
                <div class="movie-genres">
                    ${genres.map(genre => `<span class="genre-tag">${genre}</span>`).join('')}
                </div>
                <p class="movie-synopsis">${synopsis}</p>
            </div>
            <div class="movie-actions">
                ${this.getMovieActions(movie, type)}
            </div>
        `;

        // Event listeners
        this.setupMovieCardEvents(card, movie, type);

        return card;
    }

    /**
     * Retorna HTML das ações do filme
     */
    getMovieActions(movie, type) {
        if (type === 'tmdb') {
            return `
                <button class="btn-secondary view-details" data-tmdb-id="${movie.tmdb_id || movie.id}">
                    <i class="fas fa-info-circle"></i>
                    Detalhes
                </button>
                <button class="btn-primary add-to-catalog" data-tmdb-id="${movie.tmdb_id || movie.id}">
                    <i class="fas fa-plus"></i>
                    Adicionar
                </button>
            `;
        } else {
            const linksCount = movie.links ? movie.links.length : 0;
            const hasValidLinks = movie.links && movie.links.some(link => link.is_verified);

            return `
                <button class="btn-secondary view-movie-details" data-movie-id="${movie.id}">
                    <i class="fas fa-eye"></i>
                    Ver
                </button>
                <button class="btn-primary add-movie-link" data-movie-id="${movie.id}">
                    <i class="fas fa-link"></i>
                    Link (${linksCount})
                </button>
                ${hasValidLinks ? `
                    <button class="btn-success watch-movie" data-movie-id="${movie.id}">
                        <i class="fas fa-play"></i>
                        Assistir
                    </button>
                ` : ''}
                <button class="btn-stream-movie stream-movie" data-movie-id="${movie.id}">
                    <i class="fas fa-download"></i>
                    Stream
                </button>
            `;
        }
    }

    /**
     * Configura eventos dos cards de filme
     */
    setupMovieCardEvents(card, movie, type) {
        // Clique no card para ver detalhes
        card.addEventListener('click', (e) => {
            if (e.target.closest('.movie-actions')) return;
            this.showMovieDetails(movie, type);
        });

        // Botão de detalhes (TMDb)
        const viewDetailsBtn = card.querySelector('.view-details');
        if (viewDetailsBtn) {
            viewDetailsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showTmdbMovieDetails(movie.tmdb_id || movie.id);
            });
        }

        // Botão adicionar ao catálogo
        const addToCatalogBtn = card.querySelector('.add-to-catalog');
        if (addToCatalogBtn) {
            addToCatalogBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.addMovieToCatalog(movie.tmdb_id || movie.id);
            });
        }

        // Botão ver detalhes do catálogo
        const viewMovieDetailsBtn = card.querySelector('.view-movie-details');
        if (viewMovieDetailsBtn) {
            viewMovieDetailsBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showMovieDetails(movie, 'catalog');
            });
        }

        // Botão adicionar link
        const addMovieLinkBtn = card.querySelector('.add-movie-link');
        if (addMovieLinkBtn) {
            addMovieLinkBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showAddLinkModal(movie.id);
            });
        }

        // Botão assistir
        const watchMovieBtn = card.querySelector('.watch-movie');
        if (watchMovieBtn) {
            watchMovieBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showWatchOptions(movie);
            });
        }

        // Botão streaming
        const streamMovieBtn = card.querySelector('.stream-movie');
        if (streamMovieBtn) {
            streamMovieBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showStreamingOptions(movie);
            });
        }
    }

    /**
     * Exibe links
     */
    displayLinks(links) {
        const container = document.getElementById('linksList');
        if (!container) return;

        if (!links || links.length === 0) {
            this.showEmptyState('linksList', 'Nenhum link encontrado');
            return;
        }

        container.innerHTML = '';

        links.forEach(link => {
            const linkItem = this.createLinkItem(link);
            container.appendChild(linkItem);
        });
    }

    /**
     * Cria item de link
     */
    createLinkItem(link) {
        const item = document.createElement('div');
        item.className = 'link-item';

        const statusClass = link.is_verified ? 'verified' :
                           link.verification_status === 'invalid' ? 'invalid' : 'unverified';
        const statusText = link.is_verified ? 'Verificado' :
                          link.verification_status === 'invalid' ? 'Inválido' : 'Não verificado';
        const providerIcon = ui.getProviderIcon(link.provider);
        const providerName = ui.formatProviderName(link.provider);

        item.innerHTML = `
            <div class="link-header">
                <div class="link-title">
                    <i class="${providerIcon}"></i>
                    ${providerName} - ${link.resolution}
                </div>
                <div class="link-status ${statusClass}">
                    <i class="fas fa-${link.is_verified ? 'check-circle' : link.verification_status === 'invalid' ? 'times-circle' : 'clock'}"></i>
                    ${statusText}
                </div>
            </div>
            <div class="link-meta">
                <div class="link-meta-item">
                    <i class="fas fa-calendar"></i>
                    ${ui.formatDate(link.created_at)}
                </div>
                <div class="link-meta-item">
                    <i class="fas fa-download"></i>
                    ${link.download_count} downloads
                </div>
                <div class="link-meta-item">
                    <i class="fas fa-play"></i>
                    ${link.stream_count} streams
                </div>
                ${link.size_mb ? `
                    <div class="link-meta-item">
                        <i class="fas fa-hdd"></i>
                        ${ui.formatFileSize(link.size_mb * 1024 * 1024)}
                    </div>
                ` : ''}
            </div>
            <div class="link-actions">
                <button class="btn-secondary validate-link" data-link-id="${link.id}">
                    <i class="fas fa-check"></i>
                    Validar
                </button>
                ${link.is_verified ? `
                    <button class="btn-primary stream-link" data-link-id="${link.id}">
                        <i class="fas fa-play"></i>
                        Assistir
                    </button>
                    <button class="btn-success download-link" data-link-id="${link.id}">
                        <i class="fas fa-download"></i>
                        Baixar
                    </button>
                ` : ''}
                <button class="btn-error delete-link" data-link-id="${link.id}">
                    <i class="fas fa-trash"></i>
                    Remover
                </button>
            </div>
        `;

        // Event listeners
        this.setupLinkItemEvents(item, link);

        return item;
    }

    /**
     * Configura eventos dos itens de link
     */
    setupLinkItemEvents(item, link) {
        // Validar link
        const validateBtn = item.querySelector('.validate-link');
        if (validateBtn) {
            validateBtn.addEventListener('click', () => this.validateLink(link.id));
        }

        // Stream link
        const streamBtn = item.querySelector('.stream-link');
        if (streamBtn) {
            streamBtn.addEventListener('click', () => this.streamLink(link.id));
        }

        // Download link
        const downloadBtn = item.querySelector('.download-link');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadLink(link.id));
        }

        // Deletar link
        const deleteBtn = item.querySelector('.delete-link');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => this.deleteLink(link.id));
        }
    }

    /**
     * Exibe paginação
     */
    displayPagination(pagination, containerId, callback) {
        const container = document.getElementById(containerId);
        if (!container || !pagination) return;

        const { page, pages, total } = pagination;

        if (pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '';

        // Botão anterior
        if (page > 1) {
            paginationHtml += `<button class="pagination-btn" data-page="${page - 1}">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }

        // Páginas
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        if (startPage > 1) {
            paginationHtml += `<button class="pagination-btn" data-page="1">1</button>`;
            if (startPage > 2) {
                paginationHtml += `<span class="pagination-ellipsis">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `<button class="pagination-btn ${i === page ? 'active' : ''}" data-page="${i}">${i}</button>`;
        }

        if (endPage < pages) {
            if (endPage < pages - 1) {
                paginationHtml += `<span class="pagination-ellipsis">...</span>`;
            }
            paginationHtml += `<button class="pagination-btn" data-page="${pages}">${pages}</button>`;
        }

        // Botão próximo
        if (page < pages) {
            paginationHtml += `<button class="pagination-btn" data-page="${page + 1}">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        // Info
        paginationHtml += `<div class="pagination-info">
            Página ${page} de ${pages} (${total} itens)
        </div>`;

        container.innerHTML = paginationHtml;

        // Event listeners
        container.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.currentPage = parseInt(btn.dataset.page);
                callback();
            });
        });
    }

    /**
     * Mostra estado vazio
     */
    showEmptyState(containerId, message) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="welcome-message">
                <i class="fas fa-inbox"></i>
                <h3>${message}</h3>
                <p>Tente ajustar os filtros ou adicionar novos itens.</p>
            </div>
        `;
    }

    /**
     * Mostra mensagem de boas-vindas da busca
     */
    showSearchWelcome() {
        const container = document.getElementById('searchResults');
        if (!container) return;

        container.innerHTML = `
            <div class="welcome-message">
                <i class="fas fa-search"></i>
                <h3>Busque por filmes</h3>
                <p>Digite o nome de um filme para começar a buscar no TMDb</p>
            </div>
        `;
    }

    /**
     * Mostra detalhes do filme
     */
    async showMovieDetails(movie, type = 'catalog') {
        try {
            ui.showLoading('Carregando detalhes...');

            let movieData;
            if (type === 'tmdb') {
                const response = await api.getTmdbMovie(movie.tmdb_id || movie.id);
                movieData = response.data;
            } else {
                const response = await api.getMovie(movie.id);
                movieData = response.data;
            }

            this.displayMovieDetailsModal(movieData, type);
            ui.showModal('movieModal');

        } catch (error) {
            console.error('Erro ao carregar detalhes:', error);
            ui.showToast('Erro', 'Erro ao carregar detalhes do filme', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Mostra detalhes do filme do TMDb
     */
    async showTmdbMovieDetails(tmdbId) {
        try {
            ui.showLoading('Carregando detalhes...');

            const response = await api.getTmdbMovie(tmdbId);
            this.displayMovieDetailsModal(response.data, 'tmdb');
            ui.showModal('movieModal');

        } catch (error) {
            console.error('Erro ao carregar detalhes:', error);
            ui.showToast('Erro', 'Erro ao carregar detalhes do filme', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Exibe modal com detalhes do filme
     */
    displayMovieDetailsModal(movie, type) {
        const modalTitle = document.getElementById('movieModalTitle');
        const modalBody = document.getElementById('movieModalBody');

        if (modalTitle) modalTitle.textContent = movie.title;

        const posterUrl = movie.poster_path || 'https://via.placeholder.com/300x450/334155/cbd5e1?text=Sem+Poster';
        const rating = movie.vote_average ? ui.formatRating(movie.vote_average) : 'N/A';
        const runtime = movie.runtime ? ui.formatDuration(movie.runtime) : 'N/A';
        const year = movie.year || (movie.release_date ? new Date(movie.release_date).getFullYear() : 'N/A');
        const genres = Array.isArray(movie.genres) ? movie.genres : [];
        const cast = Array.isArray(movie.cast) ? movie.cast.slice(0, 5) : [];

        let modalContent = `
            <div class="movie-details">
                <div class="movie-details-poster">
                    <img src="${posterUrl}" alt="${movie.title}">
                </div>
                <div class="movie-details-info">
                    <h1>${movie.title}</h1>
                    <div class="movie-details-meta">
                        <span><i class="fas fa-calendar"></i> ${year}</span>
                        <span><i class="fas fa-star"></i> ${rating}</span>
                        <span><i class="fas fa-clock"></i> ${runtime}</span>
                        ${movie.director ? `<span><i class="fas fa-user-tie"></i> ${movie.director}</span>` : ''}
                    </div>

                    ${genres.length > 0 ? `
                        <div class="movie-genres">
                            ${genres.map(genre => `<span class="genre-tag">${typeof genre === 'string' ? genre : genre.name}</span>`).join('')}
                        </div>
                    ` : ''}

                    <div class="movie-details-synopsis">
                        <p>${movie.overview || 'Sinopse não disponível.'}</p>
                    </div>

                    ${cast.length > 0 ? `
                        <div class="movie-details-cast">
                            <h3>Elenco Principal</h3>
                            <div class="cast-list">
                                ${cast.map(actor => `<span class="cast-member">${typeof actor === 'string' ? actor : actor.name}</span>`).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <div class="movie-details-actions">
                        ${this.getModalActions(movie, type)}
                    </div>
                </div>
            </div>
        `;

        // Adicionar seção de links se for filme do catálogo
        if (type === 'catalog' && movie.links && movie.links.length > 0) {
            modalContent += `
                <div class="movie-links-section">
                    <h3>Links Disponíveis</h3>
                    <div class="movie-links-list">
                        ${movie.links.map(link => this.createMovieLinkItem(link)).join('')}
                    </div>
                </div>
            `;
        }

        if (modalBody) modalBody.innerHTML = modalContent;

        // Setup event listeners para ações do modal
        this.setupModalEvents(movie, type);
    }

    /**
     * Retorna HTML das ações do modal
     */
    getModalActions(movie, type) {
        if (type === 'tmdb') {
            return `
                <button class="btn-primary modal-add-to-catalog" data-tmdb-id="${movie.tmdb_id || movie.id}">
                    <i class="fas fa-plus"></i>
                    Adicionar ao Catálogo
                </button>
            `;
        } else {
            const hasValidLinks = movie.links && movie.links.some(link => link.is_verified);

            return `
                <button class="btn-primary modal-add-link" data-movie-id="${movie.id}">
                    <i class="fas fa-link"></i>
                    Adicionar Link
                </button>
                ${hasValidLinks ? `
                    <button class="btn-success modal-watch" data-movie-id="${movie.id}">
                        <i class="fas fa-play"></i>
                        Assistir
                    </button>
                ` : ''}
                <button class="btn-error modal-delete" data-movie-id="${movie.id}">
                    <i class="fas fa-trash"></i>
                    Remover
                </button>
            `;
        }
    }

    /**
     * Cria item de link do filme
     */
    createMovieLinkItem(link) {
        const providerIcon = ui.getProviderIcon(link.provider);
        const providerName = ui.formatProviderName(link.provider);
        const statusClass = link.is_verified ? 'verified' : 'unverified';

        return `
            <div class="movie-link-item">
                <div class="movie-link-info">
                    <span class="movie-link-provider">
                        <i class="${providerIcon}"></i>
                        ${providerName}
                    </span>
                    <span class="movie-link-resolution">${link.resolution}</span>
                    <span class="link-status ${statusClass}">
                        <i class="fas fa-${link.is_verified ? 'check-circle' : 'clock'}"></i>
                        ${link.is_verified ? 'Verificado' : 'Não verificado'}
                    </span>
                </div>
                <div class="movie-link-actions">
                    ${link.is_verified ? `
                        <button class="btn-primary stream-movie-link" data-link-id="${link.id}">
                            <i class="fas fa-play"></i>
                            Assistir
                        </button>
                        <button class="btn-success download-movie-link" data-link-id="${link.id}">
                            <i class="fas fa-download"></i>
                            Baixar
                        </button>
                    ` : `
                        <button class="btn-secondary validate-movie-link" data-link-id="${link.id}">
                            <i class="fas fa-check"></i>
                            Validar
                        </button>
                    `}
                </div>
            </div>
        `;
    }

    /**
     * Configura eventos do modal
     */
    setupModalEvents(movie, type) {
        // Adicionar ao catálogo (modal TMDb)
        const addToCatalogBtn = document.querySelector('.modal-add-to-catalog');
        if (addToCatalogBtn) {
            addToCatalogBtn.addEventListener('click', () => {
                ui.hideModal('movieModal');
                this.addMovieToCatalog(movie.tmdb_id || movie.id);
            });
        }

        // Adicionar link (modal catálogo)
        const addLinkBtn = document.querySelector('.modal-add-link');
        if (addLinkBtn) {
            addLinkBtn.addEventListener('click', () => {
                ui.hideModal('movieModal');
                this.showAddLinkModal(movie.id);
            });
        }

        // Assistir filme
        const watchBtn = document.querySelector('.modal-watch');
        if (watchBtn) {
            watchBtn.addEventListener('click', () => {
                this.showWatchOptions(movie);
            });
        }

        // Remover filme
        const deleteBtn = document.querySelector('.modal-delete');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.deleteMovie(movie.id);
            });
        }

        // Links do filme
        document.querySelectorAll('.stream-movie-link').forEach(btn => {
            btn.addEventListener('click', () => this.streamLink(btn.dataset.linkId));
        });

        document.querySelectorAll('.download-movie-link').forEach(btn => {
            btn.addEventListener('click', () => this.downloadLink(btn.dataset.linkId));
        });

        document.querySelectorAll('.validate-movie-link').forEach(btn => {
            btn.addEventListener('click', () => this.validateLink(btn.dataset.linkId));
        });
    }

    /**
     * Mostra opções de streaming para um filme
     */
    async showStreamingOptions(movie) {
        try {
            ui.showModal('streamingModal');
            document.getElementById('streamingMovieTitle').textContent = `Streaming: ${movie.title}`;

            // Mostrar loading
            document.getElementById('streamingLoading').style.display = 'block';
            document.getElementById('streamingOptions').style.display = 'none';
            document.getElementById('streamingError').style.display = 'none';

            // Buscar opções de streaming
            const response = await api.get(`/api/streaming/options/${movie.id}`);

            if (response.success && response.options) {
                this.displayStreamingOptions(response.options, response.subtitles || {}, movie);
                // Configurar event listeners após carregar as opções
                this.setupStreamingEventListeners();
            } else {
                throw new Error(response.error || 'Nenhuma opção de streaming encontrada');
            }

        } catch (error) {
            console.error('Erro ao buscar opções de streaming:', error);
            document.getElementById('streamingLoading').style.display = 'none';
            document.getElementById('streamingError').style.display = 'block';
            document.getElementById('streamingError').querySelector('p').textContent =
                error.message || 'Erro ao buscar opções de streaming';
        }
    }

    /**
     * Exibe opções de streaming organizadas por qualidade
     */
    displayStreamingOptions(options, subtitles, movie) {
        const container = document.querySelector('.streaming-qualities');
        container.innerHTML = '';

        const qualities = Object.keys(options);

        if (qualities.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">Nenhuma opção de streaming encontrada</p>';
        } else {
            qualities.forEach(quality => {
                const torrents = options[quality];
                if (torrents.length > 0) {
                    const qualityGroup = this.createQualityGroup(quality, torrents, subtitles, movie);
                    container.appendChild(qualityGroup);
                }
            });
        }

        // Esconder loading e mostrar opções
        document.getElementById('streamingLoading').style.display = 'none';
        document.getElementById('streamingOptions').style.display = 'block';
    }

    /**
     * Cria grupo de qualidade com torrents
     */
    createQualityGroup(quality, torrents, subtitles, movie) {
        const group = document.createElement('div');
        group.className = `quality-group quality-${quality.toLowerCase()}`;

        group.innerHTML = `
            <div class="quality-header">
                <span>${quality}</span>
                <span class="quality-badge">${torrents.length} opções</span>
            </div>
            <div class="torrent-list">
                ${torrents.map(torrent => this.createTorrentItem(torrent, subtitles, movie)).join('')}
            </div>
        `;

        return group;
    }

    /**
     * Cria item de torrent
     */
    createTorrentItem(torrent, subtitles, movie) {
        // Escapar aspas no magnet link
        const escapedMagnet = torrent.magnet.replace(/'/g, "\\'");

        return `
            <div class="torrent-item">
                <div class="torrent-info">
                    <div class="torrent-title">${torrent.title}</div>
                    <div class="torrent-details">
                        <span><i class="fas fa-hdd"></i> ${torrent.size || 'Tamanho desconhecido'}</span>
                        <span><i class="fas fa-users"></i> ${torrent.seeders || 0} seeders</span>
                        <span><i class="fas fa-server"></i> ${torrent.provider || 'Desconhecido'}</span>
                    </div>
                </div>
                <div class="torrent-actions">
                    <button class="btn-stream" data-magnet="${torrent.magnet}" data-quality="${torrent.quality}" data-movie-id="${movie.id}">
                        <i class="fas fa-play"></i> Stream
                    </button>
                    <button class="btn-download" data-magnet="${torrent.magnet}">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Configura event listeners para botões de streaming
     */
    setupStreamingEventListeners() {
        console.log('Configurando event listeners para streaming...');

        const streamBtns = document.querySelectorAll('.btn-stream');
        const downloadBtns = document.querySelectorAll('.btn-download');

        console.log(`Encontrados ${streamBtns.length} botões de stream e ${downloadBtns.length} botões de download`);

        // Remover listeners anteriores
        streamBtns.forEach(btn => {
            btn.replaceWith(btn.cloneNode(true));
        });
        downloadBtns.forEach(btn => {
            btn.replaceWith(btn.cloneNode(true));
        });

        // Adicionar novos listeners
        document.querySelectorAll('.btn-stream').forEach((btn, index) => {
            console.log(`Configurando listener para botão stream ${index + 1}`);
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const magnet = btn.getAttribute('data-magnet');
                const quality = btn.getAttribute('data-quality');
                const movieId = btn.getAttribute('data-movie-id');

                console.log('Clique no botão stream:', { magnet: magnet?.substring(0, 50) + '...', quality, movieId });

                if (magnet && quality && movieId) {
                    this.startStream(magnet, quality, parseInt(movieId));
                } else {
                    console.error('Dados insuficientes para stream:', { magnet: !!magnet, quality, movieId });
                }
            });
        });

        document.querySelectorAll('.btn-download').forEach((btn, index) => {
            console.log(`Configurando listener para botão download ${index + 1}`);
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const magnet = btn.getAttribute('data-magnet');

                console.log('Clique no botão download:', { magnet: magnet?.substring(0, 50) + '...' });

                if (magnet) {
                    this.downloadTorrent(magnet);
                } else {
                    console.error('Magnet link não encontrado para download');
                }
            });
        });

        console.log('Event listeners configurados com sucesso');
    }

    /**
     * Inicia streaming de um torrent
     */
    async startStream(magnetLink, quality, movieId) {
        try {
            console.log('startStream chamado:', { magnetLink, quality, movieId });

            ui.hideModal('streamingModal');
            ui.showModal('playerModal');

            const movieTitle = document.getElementById('streamingMovieTitle').textContent.replace('Streaming: ', '');
            document.getElementById('playerMovieTitle').textContent = movieTitle;

            // Mostrar loading
            document.getElementById('playerLoading').style.display = 'block';
            document.getElementById('videoPlayer').style.display = 'none';
            document.getElementById('playerError').style.display = 'none';
            document.getElementById('playerLoadingText').textContent = 'Iniciando stream...';

            // Iniciar stream com WebTorrent
            console.log('Verificando streamingManager:', typeof streamingManager);
            if (typeof streamingManager === 'undefined') {
                throw new Error('StreamingManager não está disponível. Verifique se o arquivo streaming.js foi carregado.');
            }

            console.log('Chamando streamingManager.startStream...');
            const streamResult = await streamingManager.startStream(magnetLink, {
                quality: quality,
                movieId: movieId
            });

            if (streamResult.success) {
                this.setupVideoPlayer(streamResult.file, streamResult.torrent, movieId);
            } else {
                throw new Error('Erro ao iniciar stream');
            }

        } catch (error) {
            console.error('Erro ao iniciar stream:', error);
            document.getElementById('playerLoading').style.display = 'none';
            document.getElementById('playerError').style.display = 'block';
            document.getElementById('playerError').querySelector('p').textContent =
                error.message || 'Erro ao iniciar streaming';
        }
    }

    /**
     * Configura o player de vídeo
     */
    setupVideoPlayer(file, torrent, movieId) {
        const video = document.getElementById('videoPlayer');
        const streamInfo = document.getElementById('streamInfo');

        // Criar URL do arquivo
        file.renderTo(video, (err) => {
            if (err) {
                console.error('Erro ao renderizar vídeo:', err);
                document.getElementById('playerError').style.display = 'block';
                return;
            }

            // Esconder loading e mostrar player
            document.getElementById('playerLoading').style.display = 'none';
            video.style.display = 'block';
            streamInfo.style.display = 'block';

            // Atualizar informações do stream
            this.updateStreamInfo(torrent);

            // Carregar legendas se disponíveis
            this.loadSubtitlesForPlayer(movieId);
        });
    }

    /**
     * Atualiza informações do stream
     */
    updateStreamInfo(torrent) {
        const updateStats = () => {
            const stats = streamingManager.getStreamStats();
            if (stats) {
                document.getElementById('streamQuality').textContent = `Qualidade: ${stats.progress}`;
                document.getElementById('streamSpeed').textContent = `Download: ${stats.downloadSpeed}`;
                document.getElementById('streamPeers').textContent = `Peers: ${stats.peers}`;
            }
        };

        // Atualizar a cada 2 segundos
        updateStats();
        const interval = setInterval(updateStats, 2000);

        // Limpar interval quando o modal for fechado
        const playerModal = document.getElementById('playerModal');
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (!playerModal.classList.contains('active')) {
                    clearInterval(interval);
                    observer.disconnect();
                }
            });
        });
        observer.observe(playerModal, { attributes: true, attributeFilter: ['class'] });
    }

    /**
     * Carrega legendas para o player
     */
    async loadSubtitlesForPlayer(movieId) {
        try {
            const response = await api.get(`/api/streaming/subtitles/${movieId}?languages=pt,en`);

            if (response.success && response.subtitles) {
                this.populateSubtitleSelect(response.subtitles);
            }
        } catch (error) {
            console.error('Erro ao carregar legendas:', error);
        }
    }

    /**
     * Popula select de legendas
     */
    populateSubtitleSelect(subtitles) {
        const select = document.getElementById('subtitleSelect');
        select.innerHTML = '<option value="">Sem legendas</option>';

        Object.keys(subtitles).forEach(language => {
            const subs = subtitles[language];
            if (subs.length > 0) {
                const option = document.createElement('option');
                option.value = JSON.stringify(subs[0]); // Usar a primeira legenda
                option.textContent = subs[0].languageName || language;
                select.appendChild(option);
            }
        });

        // Event listener para mudança de legenda
        select.addEventListener('change', (e) => {
            if (e.target.value) {
                const subtitleData = JSON.parse(e.target.value);
                this.loadSubtitle(subtitleData);
            } else {
                this.removeSubtitle();
            }
        });
    }

    /**
     * Carrega uma legenda específica
     */
    async loadSubtitle(subtitleData) {
        try {
            const response = await api.post('/api/streaming/subtitles/download', subtitleData);
            const video = document.getElementById('videoPlayer');

            streamingManager.loadSubtitle(video, response, subtitleData.language);

        } catch (error) {
            console.error('Erro ao carregar legenda:', error);
            ui.showToast('Erro', 'Erro ao carregar legenda', 'error');
        }
    }

    /**
     * Remove legenda atual
     */
    removeSubtitle() {
        const video = document.getElementById('videoPlayer');
        const track = video.querySelector('track');
        if (track) {
            track.remove();
        }
    }

    /**
     * Inicia download de torrent
     */
    downloadTorrent(magnetLink) {
        // Abrir magnet link (será tratado pelo cliente de torrent do sistema)
        window.open(magnetLink, '_blank');
        ui.showToast('Download', 'Magnet link aberto no cliente de torrent', 'info');
    }

    /**
     * Adiciona filme ao catálogo
     */
    async addMovieToCatalog(tmdbId) {
        try {
            ui.showLoading('Adicionando ao catálogo...');

            const response = await api.createMovie({ tmdb_id: tmdbId });

            ui.showToast('Sucesso', 'Filme adicionado ao catálogo!', 'success');
            await this.updateStats();

            // Se estiver na aba catálogo, recarrega
            if (this.currentTab === 'catalog') {
                await this.loadCatalog();
            }

        } catch (error) {
            console.error('Erro ao adicionar filme:', error);
            ui.showToast('Erro', error.message || 'Erro ao adicionar filme', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Mostra modal de adicionar link
     */
    showAddLinkModal(movieId = null) {
        this.currentMovie = movieId;

        // Limpar formulário
        const form = document.getElementById('linkForm');
        if (form) form.reset();

        ui.showModal('linkModal');
    }

    /**
     * Manipula envio do formulário de link
     */
    async handleLinkSubmit() {
        const url = document.getElementById('linkUrl')?.value?.trim();
        const resolution = document.getElementById('linkResolution')?.value;
        const quality = document.getElementById('linkQuality')?.value;
        const language = document.getElementById('linkLanguage')?.value;
        const provider = document.getElementById('linkProvider')?.value;
        const notes = document.getElementById('linkNotes')?.value?.trim();

        if (!url || !resolution) {
            ui.showToast('Aviso', 'URL e resolução são obrigatórios', 'warning');
            return;
        }

        if (!ui.isValidUrl(url)) {
            ui.showToast('Aviso', 'URL inválida', 'warning');
            return;
        }

        if (!this.currentMovie) {
            ui.showToast('Erro', 'Filme não selecionado', 'error');
            return;
        }

        try {
            ui.showLoading('Adicionando link...');

            const linkData = {
                url: url,
                resolution: resolution,
                quality: quality || '',
                language: language || 'pt-BR',
                provider: provider || '',
                user_notes: notes || ''
            };

            const response = await api.post(`/api/movies/${this.currentMovie}/links`, linkData);

            ui.hideModal('linkModal');
            ui.showToast('Sucesso', 'Link adicionado com sucesso!', 'success');

            // Recarregar dados se necessário
            if (this.currentTab === 'catalog') {
                await this.loadCatalog();
            } else if (this.currentTab === 'links') {
                await this.loadLinks();
            }

        } catch (error) {
            console.error('Erro ao adicionar link:', error);
            ui.showToast('Erro', error.message || 'Erro ao adicionar link', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Valida link
     */
    async validateLink(linkId) {
        try {
            ui.showLoading('Validando link...');

            const response = await api.validateLink(linkId);

            const message = response.data.validation.isValid ?
                'Link válido!' :
                `Link inválido: ${response.data.validation.error}`;
            const type = response.data.validation.isValid ? 'success' : 'error';

            ui.showToast('Validação', message, type);

            // Recarregar dados
            if (this.currentTab === 'links') {
                await this.loadLinks();
            }

        } catch (error) {
            console.error('Erro ao validar link:', error);
            ui.showToast('Erro', error.message || 'Erro ao validar link', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Stream link
     */
    async streamLink(linkId) {
        try {
            const response = await api.getStreamUrl(linkId);
            ui.openInNewTab(response.data.url);
            ui.showToast('Sucesso', 'Abrindo link para streaming', 'success');

        } catch (error) {
            console.error('Erro ao obter link de stream:', error);
            ui.showToast('Erro', error.message || 'Erro ao obter link', 'error');
        }
    }

    /**
     * Download link
     */
    async downloadLink(linkId) {
        try {
            const response = await api.getDownloadUrl(linkId);
            ui.openInNewTab(response.data.url);
            ui.showToast('Sucesso', 'Abrindo link para download', 'success');

        } catch (error) {
            console.error('Erro ao obter link de download:', error);
            ui.showToast('Erro', error.message || 'Erro ao obter link', 'error');
        }
    }

    /**
     * Deleta link
     */
    async deleteLink(linkId) {
        if (!ui.confirm('Tem certeza que deseja remover este link?')) {
            return;
        }

        try {
            ui.showLoading('Removendo link...');

            await api.deleteLink(linkId);
            ui.showToast('Sucesso', 'Link removido com sucesso!', 'success');

            // Recarregar dados
            if (this.currentTab === 'links') {
                await this.loadLinks();
            }

        } catch (error) {
            console.error('Erro ao remover link:', error);
            ui.showToast('Erro', error.message || 'Erro ao remover link', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Deleta filme
     */
    async deleteMovie(movieId) {
        if (!ui.confirm('Tem certeza que deseja remover este filme do catálogo?')) {
            return;
        }

        try {
            ui.showLoading('Removendo filme...');

            await api.deleteMovie(movieId);
            ui.hideModal('movieModal');
            ui.showToast('Sucesso', 'Filme removido do catálogo!', 'success');

            await this.updateStats();

            // Recarregar catálogo
            if (this.currentTab === 'catalog') {
                await this.loadCatalog();
            }

        } catch (error) {
            console.error('Erro ao remover filme:', error);
            ui.showToast('Erro', error.message || 'Erro ao remover filme', 'error');
        } finally {
            ui.hideLoading();
        }
    }

    /**
     * Mostra opções de assistir
     */
    showWatchOptions(movie) {
        if (!movie.links || movie.links.length === 0) {
            ui.showToast('Aviso', 'Nenhum link disponível para este filme', 'warning');
            return;
        }

        const validLinks = movie.links.filter(link => link.is_verified);

        if (validLinks.length === 0) {
            ui.showToast('Aviso', 'Nenhum link verificado disponível', 'warning');
            return;
        }

        if (validLinks.length === 1) {
            this.streamLink(validLinks[0].id);
        } else {
            // Se há múltiplos links, mostrar opções
            // Por simplicidade, usar o primeiro link verificado
            this.streamLink(validLinks[0].id);
        }
    }

    /**
     * Atualiza estatísticas
     */
    async updateStats() {
        try {
            const [moviesResponse, linksResponse] = await Promise.all([
                api.getMovies({ limit: 1 }),
                api.getLinks({ limit: 1 })
            ]);

            // Atualizar contadores
            const totalMovies = document.getElementById('totalMovies');
            const totalLinks = document.getElementById('totalLinks');
            const verifiedLinks = document.getElementById('verifiedLinks');

            if (totalMovies) {
                totalMovies.textContent = moviesResponse.pagination?.total || 0;
            }

            if (totalLinks) {
                totalLinks.textContent = linksResponse.pagination?.total || 0;
            }

            // Para links verificados, fazer uma consulta específica
            try {
                const verifiedResponse = await api.getLinks({ verified: 'true', limit: 1 });
                if (verifiedLinks) {
                    verifiedLinks.textContent = verifiedResponse.pagination?.total || 0;
                }
            } catch (error) {
                console.warn('Erro ao obter links verificados:', error);
            }

        } catch (error) {
            console.error('Erro ao atualizar estatísticas:', error);
        }
    }

    /**
     * Alterna tema
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Atualizar ícone
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = newTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    /**
     * Carrega tema salvo
     */
    loadSavedTheme() {
        const savedTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);

        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = savedTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
}

// Inicializar aplicação quando DOM estiver carregado
document.addEventListener('DOMContentLoaded', () => {
    window.movieIndexer = new MovieIndexer();
});
