/**
 * <PERSON><PERSON><PERSON> de <PERSON> Check
 */

const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const database = require('../config/database');
const tmdbService = require('../services/tmdbService');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * GET /api/health
 * Health check básico
 */
router.get('/', asyncHandler(async (req, res) => {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    
    res.json({
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: {
            seconds: Math.floor(uptime),
            formatted: formatUptime(uptime)
        },
        memory: {
            rss: formatBytes(memoryUsage.rss),
            heapTotal: formatBytes(memoryUsage.heapTotal),
            heapUsed: formatBytes(memoryUsage.heapUsed),
            external: formatBytes(memoryUsage.external)
        },
        node_version: process.version,
        environment: process.env.NODE_ENV || 'development'
    });
}));

/**
 * GET /api/health/detailed
 * Health check detalhado
 */
router.get('/detailed', asyncHandler(async (req, res) => {
    const checks = {
        database: false,
        tmdb: false,
        disk: false
    };
    
    const errors = [];

    // Verificar banco de dados
    try {
        const stats = await database.getStats();
        checks.database = true;
        checks.databaseStats = stats;
    } catch (error) {
        errors.push(`Database: ${error.message}`);
    }

    // Verificar API TMDb
    try {
        await tmdbService.getConfiguration();
        checks.tmdb = true;
    } catch (error) {
        errors.push(`TMDb API: ${error.message}`);
    }

    // Verificar espaço em disco
    try {
        const fs = require('fs');
        const stats = fs.statSync('./');
        checks.disk = true;
    } catch (error) {
        errors.push(`Disk: ${error.message}`);
    }

    // Verificar logs
    try {
        const logStats = logger.getLogStats();
        checks.logs = logStats;
    } catch (error) {
        errors.push(`Logs: ${error.message}`);
    }

    const allHealthy = Object.values(checks).every(check => 
        typeof check === 'boolean' ? check : true
    );

    const status = allHealthy && errors.length === 0 ? 'healthy' : 'degraded';
    const httpStatus = status === 'healthy' ? 200 : 503;

    res.status(httpStatus).json({
        success: status === 'healthy',
        status: status,
        timestamp: new Date().toISOString(),
        checks: checks,
        errors: errors,
        uptime: {
            seconds: Math.floor(process.uptime()),
            formatted: formatUptime(process.uptime())
        }
    });
}));

/**
 * GET /api/health/stats
 * Estatísticas do sistema
 */
router.get('/stats', asyncHandler(async (req, res) => {
    const stats = {
        system: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage(),
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version
        },
        application: {
            environment: process.env.NODE_ENV || 'development',
            port: process.env.PORT || 3000,
            pid: process.pid
        }
    };

    // Estatísticas do banco de dados
    try {
        stats.database = await database.getStats();
    } catch (error) {
        stats.database = { error: error.message };
    }

    // Estatísticas do cache TMDb
    try {
        stats.tmdb_cache = tmdbService.getCacheStats();
    } catch (error) {
        stats.tmdb_cache = { error: error.message };
    }

    // Estatísticas dos logs
    try {
        stats.logs = logger.getLogStats();
    } catch (error) {
        stats.logs = { error: error.message };
    }

    res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
    });
}));

/**
 * POST /api/health/cleanup
 * Limpeza de recursos
 */
router.post('/cleanup', asyncHandler(async (req, res) => {
    const results = {
        cache_cleared: false,
        logs_cleaned: false,
        gc_forced: false
    };

    try {
        // Limpar cache TMDb
        tmdbService.clearCache();
        results.cache_cleared = true;
    } catch (error) {
        logger.error('Erro ao limpar cache:', error.message);
    }

    try {
        // Limpar logs antigos
        logger.cleanOldLogs(30); // Manter últimos 30 dias
        results.logs_cleaned = true;
    } catch (error) {
        logger.error('Erro ao limpar logs:', error.message);
    }

    try {
        // Forçar garbage collection se disponível
        if (global.gc) {
            global.gc();
            results.gc_forced = true;
        }
    } catch (error) {
        logger.error('Erro ao forçar GC:', error.message);
    }

    logger.activity('cleanup', 'system', results, req.ip);

    res.json({
        success: true,
        data: results,
        message: 'Limpeza executada'
    });
}));

/**
 * GET /api/health/version
 * Informações de versão
 */
router.get('/version', asyncHandler(async (req, res) => {
    const packageJson = require('../../../package.json');
    
    res.json({
        success: true,
        data: {
            name: packageJson.name,
            version: packageJson.version,
            description: packageJson.description,
            node_version: process.version,
            environment: process.env.NODE_ENV || 'development',
            build_date: new Date().toISOString()
        }
    });
}));

/**
 * Formata tempo de uptime
 */
function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    parts.push(`${secs}s`);
    
    return parts.join(' ');
}

/**
 * Formata bytes para formato legível
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

module.exports = router;
