{"name": "movie-indexer", "version": "1.0.0", "description": "Indexador pessoal de filmes com backend Node.js e API REST", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seed.js"}, "keywords": ["movies", "indexer", "nodejs", "express", "rest-api", "tmdb"], "author": "Movie Indexer", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "sqlite3": "^5.1.6", "axios": "^1.6.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "node-cron": "^3.0.3", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0"}}