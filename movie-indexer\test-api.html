<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste da API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1e293b;
            color: #f8fafc;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #334155;
            border-radius: 8px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .result {
            background: #1e293b;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: #ef4444;
        }
        .success {
            color: #10b981;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste da API Movie Indexer</h1>
    
    <div class="test-section">
        <h2>Health Check</h2>
        <button onclick="testHealth()">Testar Health</button>
        <div id="healthResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>TMDb API</h2>
        <button onclick="testTmdbSearch()">Buscar "Matrix"</button>
        <button onclick="testTmdbGenres()">Obter Gêneros</button>
        <button onclick="testTmdbPopular()">Filmes Populares</button>
        <div id="tmdbResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Movies API</h2>
        <button onclick="testMoviesList()">Listar Filmes</button>
        <button onclick="testAddMovie()">Adicionar Filme</button>
        <div id="moviesResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Links API</h2>
        <button onclick="testLinksList()">Listar Links</button>
        <div id="linksResult" class="result"></div>
    </div>

    <script>
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `Erro: ${result.error || result.data?.error || 'Erro desconhecido'}`;
            }
        }
        
        async function testHealth() {
            const result = await makeRequest('/api/health');
            displayResult('healthResult', result);
        }
        
        async function testTmdbSearch() {
            const result = await makeRequest('/api/tmdb/search?q=matrix');
            displayResult('tmdbResult', result);
        }
        
        async function testTmdbGenres() {
            const result = await makeRequest('/api/tmdb/genres');
            displayResult('tmdbResult', result);
        }
        
        async function testTmdbPopular() {
            const result = await makeRequest('/api/tmdb/popular');
            displayResult('tmdbResult', result);
        }
        
        async function testMoviesList() {
            const result = await makeRequest('/api/movies');
            displayResult('moviesResult', result);
        }
        
        async function testAddMovie() {
            const result = await makeRequest('/api/movies', {
                method: 'POST',
                body: JSON.stringify({
                    tmdb_id: 603 // Matrix
                })
            });
            displayResult('moviesResult', result);
        }
        
        async function testLinksList() {
            const result = await makeRequest('/api/links');
            displayResult('linksResult', result);
        }
        
        // Teste automático ao carregar
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
