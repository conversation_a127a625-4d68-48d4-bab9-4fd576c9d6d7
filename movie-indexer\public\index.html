<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Movie Indexer - Indexador Pessoal de Filmes</title>
    <meta name="description" content="Indexador pessoal de filmes com catálogo, links de download e streaming">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/styles.css">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-film"></i>
                    <h1>Movie Indexer</h1>
                </div>
                
                <nav class="nav">
                    <button class="nav-btn active" data-tab="catalog">
                        <i class="fas fa-th-large"></i>
                        <span>Catálogo</span>
                    </button>
                    <button class="nav-btn" data-tab="search">
                        <i class="fas fa-search"></i>
                        <span>Buscar</span>
                    </button>
                    <button class="nav-btn" data-tab="releases">
                        <i class="fas fa-star"></i>
                        <span>Lançamentos</span>
                    </button>
                    <button class="nav-btn" data-tab="links">
                        <i class="fas fa-link"></i>
                        <span>Links</span>
                    </button>
                </nav>
                
                <div class="header-actions">
                    <button class="btn-icon" id="themeToggle" title="Alternar tema">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="btn-icon" id="settingsBtn" title="Configurações">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            
            <!-- Catalog Tab -->
            <section class="tab-content active" id="catalog-tab">
                <div class="section-header">
                    <h2><i class="fas fa-th-large"></i> Meu Catálogo</h2>
                    <div class="section-actions">
                        <div class="filters">
                            <select id="resolutionFilter" class="filter-select">
                                <option value="">Todas as resoluções</option>
                                <option value="480p">480p</option>
                                <option value="720p">720p</option>
                                <option value="1080p">1080p</option>
                                <option value="4k">4K</option>
                            </select>
                            <select id="genreFilter" class="filter-select">
                                <option value="">Todos os gêneros</option>
                            </select>
                            <input type="text" id="catalogSearch" placeholder="Buscar no catálogo..." class="search-input">
                        </div>
                        <button class="btn-primary" id="addMovieBtn">
                            <i class="fas fa-plus"></i>
                            Adicionar Filme
                        </button>
                    </div>
                </div>
                
                <div class="stats-bar">
                    <div class="stat-item">
                        <span class="stat-value" id="totalMovies">0</span>
                        <span class="stat-label">Filmes</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="totalLinks">0</span>
                        <span class="stat-label">Links</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="verifiedLinks">0</span>
                        <span class="stat-label">Verificados</span>
                    </div>
                </div>
                
                <div class="movies-grid" id="catalogGrid">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Carregando catálogo...</p>
                    </div>
                </div>
                
                <div class="pagination" id="catalogPagination"></div>
            </section>

            <!-- Search Tab -->
            <section class="tab-content" id="search-tab">
                <div class="section-header">
                    <h2><i class="fas fa-search"></i> Buscar Filmes</h2>
                </div>
                
                <div class="search-section">
                    <div class="search-bar">
                        <input type="text" id="searchInput" placeholder="Digite o nome do filme..." class="search-input-main">
                        <button class="btn-primary" id="searchBtn">
                            <i class="fas fa-search"></i>
                            Buscar
                        </button>
                    </div>
                    
                    <div class="search-filters">
                        <select id="searchYear" class="filter-select">
                            <option value="">Qualquer ano</option>
                        </select>
                        <select id="searchGenre" class="filter-select">
                            <option value="">Qualquer gênero</option>
                        </select>
                        <select id="searchSort" class="filter-select">
                            <option value="popularity.desc">Mais populares</option>
                            <option value="release_date.desc">Mais recentes</option>
                            <option value="vote_average.desc">Melhor avaliados</option>
                            <option value="title.asc">A-Z</option>
                        </select>
                    </div>
                </div>
                
                <div class="movies-grid" id="searchResults">
                    <div class="welcome-message">
                        <i class="fas fa-search"></i>
                        <h3>Busque por filmes</h3>
                        <p>Digite o nome de um filme para começar a buscar no TMDb</p>
                    </div>
                </div>
                
                <div class="pagination" id="searchPagination"></div>
            </section>

            <!-- Releases Tab -->
            <section class="tab-content" id="releases-tab">
                <div class="section-header">
                    <h2><i class="fas fa-star"></i> Lançamentos</h2>
                    <div class="section-actions">
                        <div class="tab-buttons">
                            <button class="tab-btn active" data-release-type="popular">Populares</button>
                            <button class="tab-btn" data-release-type="now-playing">Em Cartaz</button>
                            <button class="tab-btn" data-release-type="upcoming">Próximos</button>
                            <button class="tab-btn" data-release-type="top-rated">Melhores</button>
                        </div>
                    </div>
                </div>
                
                <div class="movies-grid" id="releasesGrid">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Carregando lançamentos...</p>
                    </div>
                </div>
                
                <div class="pagination" id="releasesPagination"></div>
            </section>

            <!-- Links Tab -->
            <section class="tab-content" id="links-tab">
                <div class="section-header">
                    <h2><i class="fas fa-link"></i> Gerenciar Links</h2>
                    <div class="section-actions">
                        <button class="btn-secondary" id="validateAllBtn">
                            <i class="fas fa-check-circle"></i>
                            Validar Todos
                        </button>
                        <button class="btn-primary" id="addLinkBtn">
                            <i class="fas fa-plus"></i>
                            Adicionar Link
                        </button>
                    </div>
                </div>
                
                <div class="links-filters">
                    <select id="linkProviderFilter" class="filter-select">
                        <option value="">Todos os provedores</option>
                        <option value="http">HTTP</option>
                        <option value="gdrive">Google Drive</option>
                        <option value="mega">Mega</option>
                        <option value="archive">Archive.org</option>
                        <option value="dropbox">Dropbox</option>
                    </select>
                    <select id="linkResolutionFilter" class="filter-select">
                        <option value="">Todas as resoluções</option>
                        <option value="480p">480p</option>
                        <option value="720p">720p</option>
                        <option value="1080p">1080p</option>
                        <option value="4k">4K</option>
                    </select>
                    <select id="linkStatusFilter" class="filter-select">
                        <option value="">Todos os status</option>
                        <option value="true">Verificados</option>
                        <option value="false">Não verificados</option>
                    </select>
                </div>
                
                <div class="links-list" id="linksList">
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Carregando links...</p>
                    </div>
                </div>
                
                <div class="pagination" id="linksPagination"></div>
            </section>
        </div>
    </main>

    <!-- Movie Details Modal -->
    <div class="modal" id="movieModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="movieModalTitle">Detalhes do Filme</h2>
                <button class="modal-close" id="closeMovieModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="movieModalBody">
                <!-- Conteúdo será carregado dinamicamente -->
            </div>
        </div>
    </div>

    <!-- Add Link Modal -->
    <div class="modal" id="linkModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="linkModalTitle">Adicionar Link</h2>
                <button class="modal-close" id="closeLinkModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="linkForm">
                    <div class="form-group">
                        <label for="linkUrl">URL do Link *</label>
                        <input type="url" id="linkUrl" required placeholder="https://exemplo.com/filme.mp4">
                        <small class="form-help">Suporta HTTP, Google Drive, Mega, Archive.org, Dropbox, etc.</small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="linkResolution">Resolução *</label>
                            <select id="linkResolution" required>
                                <option value="">Selecione...</option>
                                <option value="480p">480p</option>
                                <option value="720p">720p</option>
                                <option value="1080p">1080p</option>
                                <option value="4k">4K</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="linkQuality">Qualidade</label>
                            <select id="linkQuality">
                                <option value="">Selecione...</option>
                                <option value="cam">CAM</option>
                                <option value="ts">TS</option>
                                <option value="dvdrip">DVDRip</option>
                                <option value="bluray">BluRay</option>
                                <option value="webrip">WebRip</option>
                                <option value="webdl">Web-DL</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="linkLanguage">Idioma</label>
                            <select id="linkLanguage">
                                <option value="pt-BR">Português (BR)</option>
                                <option value="en">Inglês</option>
                                <option value="es">Espanhol</option>
                                <option value="fr">Francês</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="linkProvider">Provedor</label>
                            <select id="linkProvider">
                                <option value="">Auto-detectar</option>
                                <option value="http">HTTP</option>
                                <option value="gdrive">Google Drive</option>
                                <option value="mega">Mega</option>
                                <option value="archive">Archive.org</option>
                                <option value="dropbox">Dropbox</option>
                                <option value="mediafire">MediaFire</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="linkNotes">Observações</label>
                        <textarea id="linkNotes" placeholder="Observações sobre o link (opcional)"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" id="cancelLinkBtn">Cancelar</button>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-plus"></i>
                            Adicionar Link
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p id="loadingText">Carregando...</p>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Scripts -->
    <script src="/js/api.js"></script>
    <script src="/js/ui.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
