/**
 * Modelo de Filme
 */

const database = require('../config/database');
const logger = require('../utils/logger');

class Movie {
    constructor(data = {}) {
        this.id = data.id || null;
        this.tmdb_id = data.tmdb_id || null;
        this.title = data.title || '';
        this.original_title = data.original_title || '';
        this.year = data.year || null;
        this.release_date = data.release_date || null;
        this.overview = data.overview || '';
        this.poster_path = data.poster_path || null;
        this.backdrop_path = data.backdrop_path || null;
        this.genres = data.genres || [];
        this.runtime = data.runtime || null;
        this.vote_average = data.vote_average || 0;
        this.vote_count = data.vote_count || 0;
        this.popularity = data.popularity || 0;
        this.adult = data.adult || false;
        this.original_language = data.original_language || '';
        this.director = data.director || '';
        this.cast = data.cast || [];
        this.production_companies = data.production_companies || [];
        this.production_countries = data.production_countries || [];
        this.spoken_languages = data.spoken_languages || [];
        this.keywords = data.keywords || [];
        this.status = data.status || 'active';
        this.user_rating = data.user_rating || null;
        this.user_notes = data.user_notes || '';
        this.watched = data.watched || false;
        this.favorite = data.favorite || false;
        this.created_at = data.created_at || null;
        this.updated_at = data.updated_at || null;
    }

    /**
     * Salva o filme no banco de dados
     */
    async save() {
        try {
            const now = new Date().toISOString();
            
            if (this.id) {
                // Atualizar filme existente
                this.updated_at = now;
                const result = await database.run(`
                    UPDATE movies SET
                        tmdb_id = ?, title = ?, original_title = ?, year = ?,
                        release_date = ?, overview = ?, poster_path = ?, backdrop_path = ?,
                        genres = ?, runtime = ?, vote_average = ?, vote_count = ?,
                        popularity = ?, adult = ?, original_language = ?, director = ?,
                        cast = ?, production_companies = ?, production_countries = ?,
                        spoken_languages = ?, keywords = ?, status = ?, user_rating = ?,
                        user_notes = ?, watched = ?, favorite = ?, updated_at = ?
                    WHERE id = ?
                `, [
                    this.tmdb_id, this.title, this.original_title, this.year,
                    this.release_date, this.overview, this.poster_path, this.backdrop_path,
                    JSON.stringify(this.genres), this.runtime, this.vote_average, this.vote_count,
                    this.popularity, this.adult, this.original_language, this.director,
                    JSON.stringify(this.cast), JSON.stringify(this.production_companies),
                    JSON.stringify(this.production_countries), JSON.stringify(this.spoken_languages),
                    JSON.stringify(this.keywords), this.status, this.user_rating,
                    this.user_notes, this.watched, this.favorite, this.updated_at, this.id
                ]);
                
                logger.info(`Filme atualizado: ${this.title} (ID: ${this.id})`);
                return this;
            } else {
                // Criar novo filme
                this.created_at = now;
                this.updated_at = now;
                
                const result = await database.run(`
                    INSERT INTO movies (
                        tmdb_id, title, original_title, year, release_date, overview,
                        poster_path, backdrop_path, genres, runtime, vote_average,
                        vote_count, popularity, adult, original_language, director,
                        cast, production_companies, production_countries, spoken_languages,
                        keywords, status, user_rating, user_notes, watched, favorite,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    this.tmdb_id, this.title, this.original_title, this.year,
                    this.release_date, this.overview, this.poster_path, this.backdrop_path,
                    JSON.stringify(this.genres), this.runtime, this.vote_average, this.vote_count,
                    this.popularity, this.adult, this.original_language, this.director,
                    JSON.stringify(this.cast), JSON.stringify(this.production_companies),
                    JSON.stringify(this.production_countries), JSON.stringify(this.spoken_languages),
                    JSON.stringify(this.keywords), this.status, this.user_rating,
                    this.user_notes, this.watched, this.favorite, this.created_at, this.updated_at
                ]);
                
                this.id = result.id;
                logger.info(`Filme criado: ${this.title} (ID: ${this.id})`);
                return this;
            }
        } catch (error) {
            logger.error('Erro ao salvar filme:', { error: error.message, movie: this.title });
            throw error;
        }
    }

    /**
     * Busca filme por ID
     */
    static async findById(id) {
        try {
            const row = await database.get('SELECT * FROM movies WHERE id = ? AND status != "deleted"', [id]);
            return row ? new Movie(Movie.parseRow(row)) : null;
        } catch (error) {
            logger.error('Erro ao buscar filme por ID:', { error: error.message, id });
            throw error;
        }
    }

    /**
     * Busca filme por TMDb ID
     */
    static async findByTmdbId(tmdbId) {
        try {
            const row = await database.get('SELECT * FROM movies WHERE tmdb_id = ? AND status != "deleted"', [tmdbId]);
            return row ? new Movie(Movie.parseRow(row)) : null;
        } catch (error) {
            logger.error('Erro ao buscar filme por TMDb ID:', { error: error.message, tmdbId });
            throw error;
        }
    }

    /**
     * Lista filmes com filtros e paginação
     */
    static async findAll(options = {}) {
        try {
            const {
                page = 1,
                limit = 20,
                search = '',
                genre = '',
                year = '',
                resolution = '',
                watched = null,
                favorite = null,
                sortBy = 'created_at',
                sortOrder = 'DESC'
            } = options;

            const offset = (page - 1) * limit;
            let whereConditions = ['m.status != "deleted"'];
            let params = [];
            let joins = '';

            // Filtro de busca
            if (search) {
                whereConditions.push('(m.title LIKE ? OR m.original_title LIKE ? OR m.overview LIKE ?)');
                const searchTerm = `%${search}%`;
                params.push(searchTerm, searchTerm, searchTerm);
            }

            // Filtro por gênero
            if (genre) {
                whereConditions.push('m.genres LIKE ?');
                params.push(`%"${genre}"%`);
            }

            // Filtro por ano
            if (year) {
                whereConditions.push('m.year = ?');
                params.push(year);
            }

            // Filtro por resolução (através dos links)
            if (resolution) {
                joins += ' INNER JOIN movie_links ml ON m.id = ml.movie_id';
                whereConditions.push('ml.resolution = ? AND ml.status = "active"');
                params.push(resolution);
            }

            // Filtro por assistido
            if (watched !== null) {
                whereConditions.push('m.watched = ?');
                params.push(watched ? 1 : 0);
            }

            // Filtro por favorito
            if (favorite !== null) {
                whereConditions.push('m.favorite = ?');
                params.push(favorite ? 1 : 0);
            }

            const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
            
            // Query para contar total
            const countQuery = `
                SELECT COUNT(DISTINCT m.id) as total 
                FROM movies m ${joins} ${whereClause}
            `;
            const countResult = await database.get(countQuery, params);
            const total = countResult.total;

            // Query para buscar filmes
            const query = `
                SELECT DISTINCT m.* 
                FROM movies m ${joins} ${whereClause}
                ORDER BY m.${sortBy} ${sortOrder}
                LIMIT ? OFFSET ?
            `;
            params.push(limit, offset);

            const rows = await database.all(query, params);
            const movies = rows.map(row => new Movie(Movie.parseRow(row)));

            return {
                movies,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            logger.error('Erro ao listar filmes:', { error: error.message, options });
            throw error;
        }
    }

    /**
     * Busca filmes recentes
     */
    static async findRecent(limit = 10) {
        try {
            const rows = await database.all(`
                SELECT * FROM movies 
                WHERE status = "active" 
                ORDER BY created_at DESC 
                LIMIT ?
            `, [limit]);
            
            return rows.map(row => new Movie(Movie.parseRow(row)));
        } catch (error) {
            logger.error('Erro ao buscar filmes recentes:', { error: error.message });
            throw error;
        }
    }

    /**
     * Busca filmes populares
     */
    static async findPopular(limit = 10) {
        try {
            const rows = await database.all(`
                SELECT * FROM movies 
                WHERE status = "active" 
                ORDER BY popularity DESC, vote_average DESC 
                LIMIT ?
            `, [limit]);
            
            return rows.map(row => new Movie(Movie.parseRow(row)));
        } catch (error) {
            logger.error('Erro ao buscar filmes populares:', { error: error.message });
            throw error;
        }
    }

    /**
     * Remove filme (soft delete)
     */
    async delete() {
        try {
            this.status = 'deleted';
            this.updated_at = new Date().toISOString();
            
            await database.run('UPDATE movies SET status = ?, updated_at = ? WHERE id = ?', 
                [this.status, this.updated_at, this.id]);
            
            logger.info(`Filme removido: ${this.title} (ID: ${this.id})`);
            return true;
        } catch (error) {
            logger.error('Erro ao remover filme:', { error: error.message, id: this.id });
            throw error;
        }
    }

    /**
     * Converte dados do banco para objeto
     */
    static parseRow(row) {
        return {
            ...row,
            genres: row.genres ? JSON.parse(row.genres) : [],
            cast: row.cast ? JSON.parse(row.cast) : [],
            production_companies: row.production_companies ? JSON.parse(row.production_companies) : [],
            production_countries: row.production_countries ? JSON.parse(row.production_countries) : [],
            spoken_languages: row.spoken_languages ? JSON.parse(row.spoken_languages) : [],
            keywords: row.keywords ? JSON.parse(row.keywords) : [],
            adult: Boolean(row.adult),
            watched: Boolean(row.watched),
            favorite: Boolean(row.favorite)
        };
    }

    /**
     * Converte para JSON
     */
    toJSON() {
        return {
            id: this.id,
            tmdb_id: this.tmdb_id,
            title: this.title,
            original_title: this.original_title,
            year: this.year,
            release_date: this.release_date,
            overview: this.overview,
            poster_path: this.poster_path,
            backdrop_path: this.backdrop_path,
            genres: this.genres,
            runtime: this.runtime,
            vote_average: this.vote_average,
            vote_count: this.vote_count,
            popularity: this.popularity,
            adult: this.adult,
            original_language: this.original_language,
            director: this.director,
            cast: this.cast,
            production_companies: this.production_companies,
            production_countries: this.production_countries,
            spoken_languages: this.spoken_languages,
            keywords: this.keywords,
            status: this.status,
            user_rating: this.user_rating,
            user_notes: this.user_notes,
            watched: this.watched,
            favorite: this.favorite,
            created_at: this.created_at,
            updated_at: this.updated_at
        };
    }
}

module.exports = Movie;
